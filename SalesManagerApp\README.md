# Sales Manager Application / تطبيق إدارة المبيعات

A comprehensive Java desktop application for managing sales, customers, suppliers, inventory, and generating reports.

تطبيق جافا شامل لإدارة المبيعات والعملاء والموردين والمخزون وإنتاج التقارير.

## Features / المميزات

- **Customer Management / إدارة العملاء**: Add, edit, delete customers and track debts
- **Supplier Management / إدارة الموردين**: Manage suppliers and track payables
- **Product Management / إدارة المنتجات**: Inventory management with pricing
- **Sales Management / إدارة المبيعات**: Create and manage sales invoices
- **Purchase Management / إدارة المشتريات**: Handle purchase orders and invoices
- **Reports / التقارير**: Profit analysis and sales reports by customer/supplier
- **Database Integration / تكامل قاعدة البيانات**: SQLite database for data persistence

## Project Structure / هيكل المشروع

```
SalesManagerApp/
├── src/
│   ├── database/           # Database layer
│   │   ├── DBHelper.java
│   │   ├── Migrations.java
│   │   └── ReportsQueries.java
│   ├── models/             # Data models
│   │   ├── Customer.java
│   │   ├── Supplier.java
│   │   ├── Product.java
│   │   ├── Sale.java
│   │   ├── SaleItem.java
│   │   ├── Purchase.java
│   │   └── PurchaseItem.java
│   ├── ui/                 # User interface
│   │   ├── MainWindow.java
│   │   ├── CustomersPage.java
│   │   ├── SuppliersPage.java
│   │   ├── ProductsPage.java
│   │   ├── SalesPage.java
│   │   ├── PurchasesPage.java
│   │   └── ReportsPage.java
│   ├── utils/              # Utility classes
│   │   ├── Validator.java
│   │   ├── Formatter.java
│   │   └── Exporter.java
│   └── App.java            # Main entry point
├── lib/                    # Dependencies
│   └── sqlite-jdbc.jar
├── sales_manager.sqlite    # Database file (created automatically)
└── README.md
```

## Requirements / المتطلبات

- Java 8 or higher / جافا 8 أو أحدث
- SQLite JDBC Driver (included) / مشغل SQLite JDBC (مضمن)

## How to Run / كيفية التشغيل

### Prerequisites / المتطلبات المسبقة
- Java JDK 8 or higher installed / جافا JDK 8 أو أحدث مثبت
- JAVA_HOME environment variable set (optional) / متغير البيئة JAVA_HOME معين (اختياري)

### Quick Start / البدء السريع

#### Windows / ويندوز
1. Double-click `compile.bat` to compile the application / انقر مرتين على `compile.bat` لتجميع التطبيق
2. Double-click `run.bat` to start the application / انقر مرتين على `run.bat` لبدء التطبيق

#### Linux/Mac / لينكس/ماك
1. Make scripts executable / اجعل النصوص قابلة للتنفيذ:
   ```bash
   chmod +x compile.sh run.sh
   ```
2. Compile the application / جمع التطبيق:
   ```bash
   ./compile.sh
   ```
3. Run the application / تشغيل التطبيق:
   ```bash
   ./run.sh
   ```

### Manual Compilation / التجميع اليدوي

#### Windows PowerShell / ويندوز باورشيل
```powershell
# Create build directory / إنشاء مجلد البناء
New-Item -ItemType Directory -Path "build" -Force

# Compile / التجميع
javac -cp "lib/sqlite-jdbc.jar" -d build src/*.java src/**/*.java

# Run / التشغيل
java -cp "build;lib/sqlite-jdbc.jar" App
```

#### Linux/Mac Terminal / طرفية لينكس/ماك
```bash
# Create build directory / إنشاء مجلد البناء
mkdir -p build

# Compile / التجميع
javac -cp "lib/sqlite-jdbc.jar" -d build src/*.java src/**/*.java

# Run / التشغيل
java -cp "build:lib/sqlite-jdbc.jar" App
```

## Application Features / مميزات التطبيق

### Current Implementation / التنفيذ الحالي
- ✅ **Database Layer** / طبقة قاعدة البيانات: Complete SQLite integration with all tables
- ✅ **Data Models** / نماذج البيانات: Full CRUD operations for all entities
- ✅ **Utility Classes** / فئات المساعدة: Validation, formatting, and export utilities
- ✅ **Main Window** / النافذة الرئيسية: Tabbed interface with navigation
- ✅ **Customer Management** / إدارة العملاء: Fully functional customer CRUD operations

### Under Development / قيد التطوير
- 🚧 **Supplier Management** / إدارة الموردين: Interface implementation pending
- 🚧 **Product Management** / إدارة المنتجات: Interface implementation pending
- 🚧 **Sales Management** / إدارة المبيعات: Interface implementation pending
- 🚧 **Purchase Management** / إدارة المشتريات: Interface implementation pending
- 🚧 **Reports** / التقارير: Interface implementation pending

## Database Schema / مخطط قاعدة البيانات

The application uses SQLite with the following tables:

### customers / العملاء
- `id` - Primary key / المفتاح الأساسي
- `name` - Customer name / اسم العميل
- `phone` - Phone number / رقم الهاتف
- `email` - Email address / البريد الإلكتروني
- `address` - Address / العنوان
- `debt_balance` - Outstanding debt / الديون المستحقة
- `created_at`, `updated_at` - Timestamps / الطوابع الزمنية

### suppliers / الموردين
- `id` - Primary key / المفتاح الأساسي
- `name` - Supplier name / اسم المورد
- `phone` - Phone number / رقم الهاتف
- `email` - Email address / البريد الإلكتروني
- `address` - Address / العنوان
- `payable_balance` - Amount owed to supplier / المبلغ المستحق للمورد
- `created_at`, `updated_at` - Timestamps / الطوابع الزمنية

### products / المنتجات
- `id` - Primary key / المفتاح الأساسي
- `name` - Product name / اسم المنتج
- `description` - Product description / وصف المنتج
- `category` - Product category / فئة المنتج
- `purchase_price` - Purchase price / سعر الشراء
- `selling_price` - Selling price / سعر البيع
- `stock_quantity` - Current stock / المخزون الحالي
- `min_stock_level` - Minimum stock alert level / مستوى تنبيه المخزون الأدنى
- `created_at`, `updated_at` - Timestamps / الطوابع الزمنية

### sales / المبيعات
- `id` - Primary key / المفتاح الأساسي
- `customer_id` - Foreign key to customers / مفتاح خارجي للعملاء
- `sale_date` - Sale date / تاريخ البيع
- `total_amount` - Total invoice amount / إجمالي مبلغ الفاتورة
- `paid_amount` - Amount paid / المبلغ المدفوع
- `discount` - Discount amount / مبلغ الخصم
- `notes` - Additional notes / ملاحظات إضافية
- `created_at` - Creation timestamp / طابع زمني للإنشاء

### sale_items / عناصر المبيعات
- `id` - Primary key / المفتاح الأساسي
- `sale_id` - Foreign key to sales / مفتاح خارجي للمبيعات
- `product_id` - Foreign key to products / مفتاح خارجي للمنتجات
- `quantity` - Quantity sold / الكمية المباعة
- `unit_price` - Price per unit / السعر لكل وحدة
- `total_price` - Total line amount / إجمالي مبلغ السطر

### purchases / المشتريات
- `id` - Primary key / المفتاح الأساسي
- `supplier_id` - Foreign key to suppliers / مفتاح خارجي للموردين
- `purchase_date` - Purchase date / تاريخ الشراء
- `total_amount` - Total invoice amount / إجمالي مبلغ الفاتورة
- `paid_amount` - Amount paid / المبلغ المدفوع
- `notes` - Additional notes / ملاحظات إضافية
- `created_at` - Creation timestamp / طابع زمني للإنشاء

### purchase_items / عناصر المشتريات
- `id` - Primary key / المفتاح الأساسي
- `purchase_id` - Foreign key to purchases / مفتاح خارجي للمشتريات
- `product_id` - Foreign key to products / مفتاح خارجي للمنتجات
- `quantity` - Quantity purchased / الكمية المشتراة
- `unit_price` - Price per unit / السعر لكل وحدة
- `total_price` - Total line amount / إجمالي مبلغ السطر

## Usage Instructions / تعليمات الاستخدام

### First Run / التشغيل الأول
1. The application will automatically create the SQLite database file `sales_manager.sqlite`
2. All required tables will be created automatically
3. The main window will open with tabbed navigation

### Customer Management / إدارة العملاء
- **Add Customer** / إضافة عميل: Fill in the form and click "Add"
- **Edit Customer** / تعديل عميل: Select a customer from the table, modify the form, and click "Edit"
- **Delete Customer** / حذف عميل: Select a customer and click "Delete"
- **Validation** / التحقق: The system validates names, phone numbers, and email addresses

### Navigation / التنقل
- Use the tabs at the top to switch between different sections
- Each tab represents a different module of the application
- The status bar shows the current time and application status

## Troubleshooting / استكشاف الأخطاء

### Common Issues / المشاكل الشائعة

#### Java Not Found / جافا غير موجود
```
Error: Java is not installed or not in PATH
```
**Solution / الحل:**
1. Install Java JDK 8 or higher from [Oracle](https://www.oracle.com/java/technologies/downloads/) or [OpenJDK](https://openjdk.org/)
2. Add Java to your system PATH
3. Verify installation: `java -version`

#### Compilation Errors / أخطاء التجميع
```
javac: command not found
```
**Solution / الحل:**
1. Ensure JDK (not just JRE) is installed
2. Add JDK bin directory to PATH
3. On Windows: Add `C:\Program Files\Java\jdk-XX\bin` to PATH

#### Database Errors / أخطاء قاعدة البيانات
```
SQLException: database is locked
```
**Solution / الحل:**
1. Close any other instances of the application
2. Delete the `sales_manager.sqlite` file and restart (will recreate empty database)
3. Check file permissions

#### UI Display Issues / مشاكل عرض الواجهة
**Arabic Text Not Displaying Properly / النص العربي لا يظهر بشكل صحيح:**
1. Ensure your system supports Arabic fonts
2. Install Arabic language pack if needed
3. The application uses Arial font which should support Arabic

### Performance Tips / نصائح الأداء
- The application stores data locally in SQLite for fast access
- Regular backups are recommended (copy the `sales_manager.sqlite` file)
- For large datasets, consider periodic cleanup of old records

## Development Notes / ملاحظات التطوير

### Architecture / الهيكل المعماري
- **MVC Pattern** / نمط MVC: Models handle data, Views handle UI, Controllers handle logic
- **Database Layer** / طبقة قاعدة البيانات: Centralized database operations
- **Utility Classes** / فئات المساعدة: Reusable validation, formatting, and export functions

### Code Organization / تنظيم الكود
- `models/` - Data models with database operations
- `ui/` - User interface components
- `database/` - Database connection and query management
- `utils/` - Utility functions for validation, formatting, export

### Future Enhancements / التحسينات المستقبلية
- Complete implementation of all management pages
- Advanced reporting with charts and graphs
- Multi-user support with authentication
- Backup and restore functionality
- Print receipt and invoice templates
- Barcode scanning integration
- Multi-language support

## License / الترخيص

This project is open source and available under the MIT License.

## Contributing / المساهمة

Contributions are welcome! Please feel free to submit a Pull Request.

### How to Contribute / كيفية المساهمة
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Areas Needing Help / المجالات التي تحتاج مساعدة
- Complete implementation of remaining UI pages
- Add unit tests
- Improve Arabic language support
- Add more export formats
- Performance optimizations
