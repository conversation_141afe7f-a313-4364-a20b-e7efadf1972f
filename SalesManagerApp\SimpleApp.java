import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * تطبيق مبسط لاختبار Java Swing
 * Simple application to test Java Swing
 */
public class SimpleApp extends JFrame {
    
    private JLabel statusLabel;
    private int clickCount = 0;
    
    public SimpleApp() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
    }
    
    private void initializeComponents() {
        setTitle("نظام إدارة المبيعات - اختبار / Sales Management System - Test");
        setSize(600, 400);
        setMinimumSize(new Dimension(500, 300));
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Header
        JPanel headerPanel = new JPanel();
        headerPanel.setBackground(new Color(102, 126, 234));
        headerPanel.setPreferredSize(new Dimension(0, 80));
        
        JLabel titleLabel = new JLabel("نظام إدارة المبيعات", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);
        headerPanel.add(titleLabel);
        
        add(headerPanel, BorderLayout.NORTH);
        
        // Center content
        JPanel centerPanel = new JPanel(new GridBagLayout());
        centerPanel.setBackground(Color.WHITE);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // Welcome message
        JLabel welcomeLabel = new JLabel("مرحباً بك في تطبيق إدارة المبيعات!", JLabel.CENTER);
        welcomeLabel.setFont(new Font("Arial", Font.PLAIN, 18));
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        centerPanel.add(welcomeLabel, gbc);
        
        // English welcome
        JLabel welcomeEnLabel = new JLabel("Welcome to Sales Management System!", JLabel.CENTER);
        welcomeEnLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        welcomeEnLabel.setForeground(Color.GRAY);
        gbc.gridy = 1;
        centerPanel.add(welcomeEnLabel, gbc);
        
        // Test button
        JButton testButton = new JButton("اختبار / Test");
        testButton.setFont(new Font("Arial", Font.BOLD, 16));
        testButton.setPreferredSize(new Dimension(200, 50));
        testButton.setBackground(new Color(40, 167, 69));
        testButton.setForeground(Color.WHITE);
        testButton.setFocusPainted(false);
        gbc.gridy = 2; gbc.gridwidth = 1;
        centerPanel.add(testButton, gbc);
        
        // Info button
        JButton infoButton = new JButton("معلومات / Info");
        infoButton.setFont(new Font("Arial", Font.BOLD, 16));
        infoButton.setPreferredSize(new Dimension(200, 50));
        infoButton.setBackground(new Color(23, 162, 184));
        infoButton.setForeground(Color.WHITE);
        infoButton.setFocusPainted(false);
        gbc.gridx = 1;
        centerPanel.add(infoButton, gbc);
        
        add(centerPanel, BorderLayout.CENTER);
        
        // Status bar
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBackground(new Color(248, 249, 250));
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        statusLabel = new JLabel("جاهز / Ready");
        statusLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        statusPanel.add(statusLabel);
        
        add(statusPanel, BorderLayout.SOUTH);
        
        // Setup button actions
        testButton.addActionListener(e -> {
            clickCount++;
            statusLabel.setText("تم النقر " + clickCount + " مرة / Clicked " + clickCount + " times");
            
            if (clickCount == 1) {
                JOptionPane.showMessageDialog(this, 
                    "Java يعمل بشكل صحيح!\nJava is working correctly!",
                    "نجح الاختبار / Test Success", 
                    JOptionPane.INFORMATION_MESSAGE);
            } else if (clickCount == 5) {
                JOptionPane.showMessageDialog(this, 
                    "ممتاز! التطبيق يعمل بشكل مثالي\nExcellent! The application works perfectly",
                    "تهانينا / Congratulations", 
                    JOptionPane.INFORMATION_MESSAGE);
            }
        });
        
        infoButton.addActionListener(e -> {
            String message = """
                نظام إدارة المبيعات
                Sales Management System
                
                الإصدار التجريبي / Demo Version
                
                المميزات المتاحة:
                ✓ واجهة مستخدم تفاعلية
                ✓ دعم اللغة العربية والإنجليزية
                ✓ تصميم احترافي
                
                Available Features:
                ✓ Interactive user interface
                ✓ Arabic and English support
                ✓ Professional design
                
                Java Version: """ + System.getProperty("java.version") + """
                
                OS: """ + System.getProperty("os.name");
                
            JOptionPane.showMessageDialog(this, message, "معلومات / Information", JOptionPane.INFORMATION_MESSAGE);
        });
    }
    
    private void setupEventHandlers() {
        // Window closing confirmation
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent windowEvent) {
                int option = JOptionPane.showConfirmDialog(
                    SimpleApp.this,
                    "هل تريد إغلاق التطبيق؟\nDo you want to exit the application?",
                    "تأكيد الخروج / Confirm Exit",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.QUESTION_MESSAGE
                );
                
                if (option == JOptionPane.YES_OPTION) {
                    System.exit(0);
                }
            }
        });
    }
    
    public static void main(String[] args) {
        // Run on EDT
        SwingUtilities.invokeLater(() -> {
            try {
                new SimpleApp().setVisible(true);
                System.out.println("تم تشغيل التطبيق بنجاح!");
                System.out.println("Application started successfully!");
            } catch (Exception e) {
                System.err.println("خطأ في تشغيل التطبيق: " + e.getMessage());
                System.err.println("Error starting application: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
