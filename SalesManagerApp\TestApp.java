import database.DBHelper;
import database.Migrations;
import models.Customer;
import models.Product;
import models.Supplier;
import utils.Formatter;
import utils.Validator;

/**
 * فئة اختبار التطبيق
 * Application test class
 */
public class TestApp {
    
    public static void main(String[] args) {
        System.out.println("=== اختبار تطبيق إدارة المبيعات ===");
        System.out.println("=== Sales Manager Application Test ===");
        System.out.println();
        
        try {
            // Test database initialization
            testDatabaseInitialization();
            
            // Test validation utilities
            testValidationUtilities();
            
            // Test formatting utilities
            testFormattingUtilities();
            
            // Test customer operations
            testCustomerOperations();
            
            // Test product operations
            testProductOperations();
            
            // Test supplier operations
            testSupplierOperations();
            
            System.out.println("=== جميع الاختبارات نجحت! ===");
            System.out.println("=== All tests passed! ===");
            
        } catch (Exception e) {
            System.err.println("=== فشل الاختبار: " + e.getMessage() + " ===");
            System.err.println("=== Test failed: " + e.getMessage() + " ===");
            e.printStackTrace();
        } finally {
            // Close database connection
            DBHelper.closeConnection();
        }
    }
    
    private static void testDatabaseInitialization() {
        System.out.println("1. اختبار تهيئة قاعدة البيانات / Testing database initialization...");
        
        DBHelper.initializeDatabase();
        Migrations.createTables();
        
        System.out.println("   ✓ تم تهيئة قاعدة البيانات بنجاح / Database initialized successfully");
        System.out.println();
    }
    
    private static void testValidationUtilities() {
        System.out.println("2. اختبار أدوات التحقق / Testing validation utilities...");
        
        // Test name validation
        assert Validator.isValidName("أحمد محمد") : "Arabic name validation failed";
        assert Validator.isValidName("Ahmed Mohamed") : "English name validation failed";
        assert !Validator.isValidName("A") : "Short name validation failed";
        
        // Test email validation
        assert Validator.isValidEmail("<EMAIL>") : "Email validation failed";
        assert !Validator.isValidEmail("invalid-email") : "Invalid email validation failed";
        
        // Test phone validation
        assert Validator.isValidPhone("0501234567") : "Phone validation failed";
        assert Validator.isValidPhone("+966501234567") : "International phone validation failed";
        
        // Test price validation
        assert Validator.isValidPrice(100.50) : "Price validation failed";
        assert !Validator.isValidPrice(-10) : "Negative price validation failed";
        
        System.out.println("   ✓ جميع اختبارات التحقق نجحت / All validation tests passed");
        System.out.println();
    }
    
    private static void testFormattingUtilities() {
        System.out.println("3. اختبار أدوات التنسيق / Testing formatting utilities...");
        
        // Test currency formatting
        String currency = Formatter.formatCurrency(1234.56);
        assert currency.contains("1,234.56") : "Currency formatting failed: " + currency;
        
        // Test percentage formatting
        String percentage = Formatter.formatPercentage(25.5);
        assert percentage.contains("25.50%") : "Percentage formatting failed: " + percentage;
        
        // Test phone formatting
        String phone = Formatter.formatPhone("0501234567");
        assert phone.length() > 0 : "Phone formatting failed";
        
        // Test payment status
        String status = Formatter.formatPaymentStatus(100, 100);
        assert status.contains("Fully Paid") : "Payment status formatting failed";
        
        System.out.println("   ✓ جميع اختبارات التنسيق نجحت / All formatting tests passed");
        System.out.println();
    }
    
    private static void testCustomerOperations() {
        System.out.println("4. اختبار عمليات العملاء / Testing customer operations...");
        
        // Create test customer
        Customer customer = new Customer("أحمد محمد", "0501234567", "<EMAIL>", "الرياض");
        
        // Test save (insert)
        boolean saved = customer.save();
        assert saved : "Customer save failed";
        assert customer.getId() > 0 : "Customer ID not generated";
        
        // Test find by ID
        Customer foundCustomer = Customer.findById(customer.getId());
        assert foundCustomer != null : "Customer not found by ID";
        assert foundCustomer.getName().equals("أحمد محمد") : "Customer name mismatch";
        
        // Test update
        foundCustomer.setPhone("0509876543");
        boolean updated = foundCustomer.save();
        assert updated : "Customer update failed";
        
        // Test find all
        var customers = Customer.findAll();
        assert customers.size() > 0 : "No customers found";
        
        // Test delete
        boolean deleted = foundCustomer.delete();
        assert deleted : "Customer delete failed";
        
        System.out.println("   ✓ جميع اختبارات العملاء نجحت / All customer tests passed");
        System.out.println();
    }
    
    private static void testProductOperations() {
        System.out.println("5. اختبار عمليات المنتجات / Testing product operations...");
        
        // Create test product
        Product product = new Product("لابتوب", "لابتوب للألعاب", "إلكترونيات", 2000.0, 2500.0, 10, 2);
        
        // Test save (insert)
        boolean saved = product.save();
        assert saved : "Product save failed";
        assert product.getId() > 0 : "Product ID not generated";
        
        // Test calculated properties
        double margin = product.getProfitMargin();
        assert margin == 25.0 : "Profit margin calculation failed: " + margin;
        
        boolean lowStock = product.isLowStock();
        assert !lowStock : "Low stock calculation failed";
        
        // Test find by ID
        Product foundProduct = Product.findById(product.getId());
        assert foundProduct != null : "Product not found by ID";
        assert foundProduct.getName().equals("لابتوب") : "Product name mismatch";
        
        // Test update
        foundProduct.setStockQuantity(1);
        boolean updated = foundProduct.save();
        assert updated : "Product update failed";
        
        // Test low stock after update
        assert foundProduct.isLowStock() : "Low stock detection failed";
        
        // Test find all
        var products = Product.findAll();
        assert products.size() > 0 : "No products found";
        
        // Test delete
        boolean deleted = foundProduct.delete();
        assert deleted : "Product delete failed";
        
        System.out.println("   ✓ جميع اختبارات المنتجات نجحت / All product tests passed");
        System.out.println();
    }
    
    private static void testSupplierOperations() {
        System.out.println("6. اختبار عمليات الموردين / Testing supplier operations...");
        
        // Create test supplier
        Supplier supplier = new Supplier("شركة التقنية", "0112345678", "<EMAIL>", "الرياض");
        
        // Test save (insert)
        boolean saved = supplier.save();
        assert saved : "Supplier save failed";
        assert supplier.getId() > 0 : "Supplier ID not generated";
        
        // Test find by ID
        Supplier foundSupplier = Supplier.findById(supplier.getId());
        assert foundSupplier != null : "Supplier not found by ID";
        assert foundSupplier.getName().equals("شركة التقنية") : "Supplier name mismatch";
        
        // Test update
        foundSupplier.setPayableBalance(1000.0);
        boolean updated = foundSupplier.save();
        assert updated : "Supplier update failed";
        
        // Test find all
        var suppliers = Supplier.findAll();
        assert suppliers.size() > 0 : "No suppliers found";
        
        // Test delete
        boolean deleted = foundSupplier.delete();
        assert deleted : "Supplier delete failed";
        
        System.out.println("   ✓ جميع اختبارات الموردين نجحت / All supplier tests passed");
        System.out.println();
    }
}
