@echo off
echo Compiling Sales Manager Application...
echo تجميع تطبيق إدارة المبيعات...

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo خطأ: جافا غير مثبت أو غير موجود في PATH
    echo Please install Java JDK 8 or higher
    echo يرجى تثبيت Java JDK 8 أو أحدث
    exit /b 1
)

REM Create output directory
if not exist "build" mkdir build

REM Compile all Java files
javac -cp "lib/sqlite-jdbc.jar" -d build src/*.java src/**/*.java

if %errorlevel% equ 0 (
    echo Compilation successful!
    echo تم التجميع بنجاح!
    echo.
    echo To run the application, use: run.bat
    echo لتشغيل التطبيق، استخدم: run.bat
) else (
    echo Compilation failed!
    echo فشل التجميع!
)