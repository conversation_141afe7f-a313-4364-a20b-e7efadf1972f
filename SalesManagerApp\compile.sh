#!/bin/bash

echo "Compiling Sales Manager Application..."
echo "تجميع تطبيق إدارة المبيعات..."

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "خطأ: جافا غير مثبت أو غير موجود في PATH"
    echo "Please install Java JDK 8 or higher"
    echo "يرجى تثبيت Java JDK 8 أو أحدث"
    exit 1
fi

# Create output directory
mkdir -p build

# Compile all Java files
javac -cp "lib/sqlite-jdbc.jar" -d build src/*.java src/**/*.java

if [ $? -eq 0 ]; then
    echo "Compilation successful!"
    echo "تم التجميع بنجاح!"
    echo ""
    echo "To run the application, use: ./run.sh"
    echo "لتشغيل التطبيق، استخدم: ./run.sh"
    chmod +x run.sh
else
    echo "Compilation failed!"
    echo "فشل التجميع!"
    exit 1
fi
