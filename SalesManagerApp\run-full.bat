@echo off
echo Starting Full Sales Manager Application...
echo بدء تشغيل تطبيق إدارة المبيعات الكامل...

REM Set Java path
set JAVA_HOME=C:\Program Files\Java\jdk-24
set JAVA_BIN=%JAVA_HOME%\bin

REM Check if Java is available
if not exist "%JAVA_BIN%\java.exe" (
    echo Error: Java not found at %JAVA_HOME%
    echo خطأ: جافا غير موجود في %JAVA_HOME%
    echo Please check Java installation
    echo يرجى التحقق من تثبيت جافا
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "build\App.class" (
    echo Error: Application not compiled. Please run setup-full.bat first.
    echo خطأ: التطبيق غير مجمع. يرجى تشغيل setup-full.bat أولاً.
    pause
    exit /b 1
)

REM Check if SQLite JDBC exists
if not exist "lib\sqlite-jdbc.jar" (
    echo Error: SQLite JDBC driver not found. Please run setup-full.bat first.
    echo خطأ: مشغل SQLite JDBC غير موجود. يرجى تشغيل setup-full.bat أولاً.
    pause
    exit /b 1
)

echo Running full application...
echo تشغيل التطبيق الكامل...
"%JAVA_BIN%\java.exe" -cp "build;lib\sqlite-jdbc.jar" App

if %errorlevel% neq 0 (
    echo Error running application!
    echo خطأ في تشغيل التطبيق!
    pause
)
