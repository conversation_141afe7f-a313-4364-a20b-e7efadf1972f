@echo off
echo Starting Simple Sales Manager Application...
echo بدء تشغيل تطبيق إدارة المبيعات المبسط...

REM Set Java path
set JAVA_HOME=C:\Program Files\Java\jdk-24
set JAVA_BIN=%JAVA_HOME%\bin

REM Check if Java is available
if not exist "%JAVA_BIN%\java.exe" (
    echo Error: Java not found at %JAVA_HOME%
    echo خطأ: جافا غير موجود في %JAVA_HOME%
    echo Please check Java installation
    echo يرجى التحقق من تثبيت جافا
    pause
    exit /b 1
)

REM Check if compiled classes exist
if not exist "build\SimpleApp.class" (
    echo Error: Application not compiled. Compiling now...
    echo خطأ: التطبيق غير مجمع. جاري التجميع الآن...
    
    if not exist "build" mkdir build
    "%JAVA_BIN%\javac.exe" -d build SimpleApp.java
    
    if %errorlevel% neq 0 (
        echo Compilation failed!
        echo فشل التجميع!
        pause
        exit /b 1
    )
    
    echo Compilation successful!
    echo تم التجميع بنجاح!
)

echo Running application...
echo تشغيل التطبيق...
"%JAVA_BIN%\java.exe" -cp build SimpleApp

if %errorlevel% neq 0 (
    echo Error running application!
    echo خطأ في تشغيل التطبيق!
    pause
)
