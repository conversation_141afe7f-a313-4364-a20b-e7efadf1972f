#!/bin/bash

echo "Starting Sales Manager Application..."
echo "بدء تشغيل تطبيق إدارة المبيعات..."

# Check if compiled classes exist
if [ ! -d "build" ]; then
    echo "Error: Application not compiled. Please run ./compile.sh first."
    echo "خطأ: التطبيق غير مجمع. يرجى تشغيل ./compile.sh أولاً."
    exit 1
fi

# Run the application
java -cp "build:lib/sqlite-jdbc.jar" App

if [ $? -ne 0 ]; then
    echo "Error running application!"
    echo "خطأ في تشغيل التطبيق!"
fi
