@echo off
echo Setting up Full Sales Manager Application...
echo إعداد تطبيق إدارة المبيعات الكامل...

REM Set Java path
set JAVA_HOME=C:\Program Files\Java\jdk-24
set JAVA_BIN=%JAVA_HOME%\bin

REM Check if Java is available
if not exist "%JAVA_BIN%\java.exe" (
    echo Error: Java not found at %JAVA_HOME%
    echo خطأ: جافا غير موجود في %JAVA_HOME%
    echo Please check Java installation
    echo يرجى التحقق من تثبيت جافا
    pause
    exit /b 1
)

REM Create directories
if not exist "build" mkdir build
if not exist "lib" mkdir lib

REM Download SQLite JDBC if not exists
if not exist "lib\sqlite-jdbc.jar" (
    echo Downloading SQLite JDBC driver...
    echo تحميل مشغل SQLite JDBC...
    
    powershell -Command "try { Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar' -OutFile 'lib\sqlite-jdbc.jar' -ErrorAction Stop; Write-Host 'Download successful!' } catch { Write-Host 'Download failed. Please download manually from: https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar' }"
    
    if not exist "lib\sqlite-jdbc.jar" (
        echo Download failed. Please download SQLite JDBC manually.
        echo فشل التحميل. يرجى تحميل SQLite JDBC يدوياً.
        echo URL: https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar
        pause
        exit /b 1
    )
)

REM Compile utility classes first
echo Compiling utility classes...
echo تجميع فئات المساعدة...
"%JAVA_BIN%\javac.exe" -d build src\utils\*.java

if %errorlevel% neq 0 (
    echo Failed to compile utility classes!
    echo فشل في تجميع فئات المساعدة!
    pause
    exit /b 1
)

REM Compile database classes
echo Compiling database classes...
echo تجميع فئات قاعدة البيانات...
"%JAVA_BIN%\javac.exe" -cp "lib\sqlite-jdbc.jar;build" -d build src\database\*.java

if %errorlevel% neq 0 (
    echo Failed to compile database classes!
    echo فشل في تجميع فئات قاعدة البيانات!
    pause
    exit /b 1
)

REM Compile model classes
echo Compiling model classes...
echo تجميع فئات النماذج...
"%JAVA_BIN%\javac.exe" -cp "lib\sqlite-jdbc.jar;build" -d build src\models\*.java

if %errorlevel% neq 0 (
    echo Failed to compile model classes!
    echo فشل في تجميع فئات النماذج!
    pause
    exit /b 1
)

REM Compile UI classes
echo Compiling UI classes...
echo تجميع فئات الواجهة...
"%JAVA_BIN%\javac.exe" -cp "lib\sqlite-jdbc.jar;build" -d build src\ui\*.java

if %errorlevel% neq 0 (
    echo Failed to compile UI classes!
    echo فشل في تجميع فئات الواجهة!
    pause
    exit /b 1
)

REM Compile main application
echo Compiling main application...
echo تجميع التطبيق الرئيسي...
"%JAVA_BIN%\javac.exe" -cp "lib\sqlite-jdbc.jar;build" -d build src\App.java

if %errorlevel% equ 0 (
    echo Full application compilation successful!
    echo تم تجميع التطبيق الكامل بنجاح!
    echo.
    echo To run the full application, use: run-full.bat
    echo لتشغيل التطبيق الكامل، استخدم: run-full.bat
) else (
    echo Compilation failed!
    echo فشل التجميع!
)

pause
