import ui.MainWindow;
import database.DBHelper;
import database.Migrations;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.UnsupportedLookAndFeelException;

/**
 * نقطة البداية الرئيسية لتطبيق إدارة المبيعات
 * Main entry point for the Sales Manager Application
 */
public class App {
    public static void main(String[] args) {
        // تعيين مظهر النظام
        // Set system look and feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (ClassNotFoundException | InstantiationException | 
                 IllegalAccessException | UnsupportedLookAndFeelException e) {
            System.err.println("خطأ في تعيين مظهر النظام: " + e.getMessage());
            System.err.println("Error setting system look and feel: " + e.getMessage());
        }

        // تشغيل التطبيق في خيط واجهة المستخدم
        // Run application on EDT (Event Dispatch Thread)
        SwingUtilities.invokeLater(() -> {
            try {
                // تهيئة قاعدة البيانات
                // Initialize database
                DBHelper.initializeDatabase();
                Migrations.createTables();
                
                // إنشاء وعرض النافذة الرئيسية
                // Create and show main window
                MainWindow mainWindow = new MainWindow();
                mainWindow.setVisible(true);
                
                System.out.println("تم تشغيل تطبيق إدارة المبيعات بنجاح");
                System.out.println("Sales Manager Application started successfully");
                
            } catch (Exception e) {
                System.err.println("خطأ في تشغيل التطبيق: " + e.getMessage());
                System.err.println("Error starting application: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
