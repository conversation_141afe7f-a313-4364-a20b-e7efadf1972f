package database;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * مساعد قاعدة البيانات - إدارة الاتصالات مع قاعدة بيانات SQLite
 * Database Helper - Manages connections to SQLite database
 */
public class DBHelper {
    private static final String DATABASE_URL = "********************************";
    private static Connection connection = null;
    
    /**
     * تهيئة قاعدة البيانات وإنشاء الاتصال
     * Initialize database and create connection
     */
    public static void initializeDatabase() {
        try {
            // تحميل مشغل SQLite JDBC
            // Load SQLite JDBC driver
            Class.forName("org.sqlite.JDBC");
            
            // إنشاء الاتصال
            // Create connection
            connection = DriverManager.getConnection(DATABASE_URL);
            
            // تفعيل المفاتيح الخارجية
            // Enable foreign keys
            Statement stmt = connection.createStatement();
            stmt.execute("PRAGMA foreign_keys = ON");
            stmt.close();
            
            System.out.println("تم الاتصال بقاعدة البيانات بنجاح");
            System.out.println("Database connection established successfully");
            
        } catch (ClassNotFoundException e) {
            System.err.println("خطأ: لم يتم العثور على مشغل SQLite JDBC");
            System.err.println("Error: SQLite JDBC driver not found");
            e.printStackTrace();
        } catch (SQLException e) {
            System.err.println("خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            System.err.println("Database connection error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * الحصول على اتصال قاعدة البيانات
     * Get database connection
     * @return Connection object
     */
    public static Connection getConnection() {
        try {
            // التحقق من صحة الاتصال
            // Check if connection is valid
            if (connection == null || connection.isClosed()) {
                initializeDatabase();
            }
        } catch (SQLException e) {
            System.err.println("خطأ في التحقق من اتصال قاعدة البيانات: " + e.getMessage());
            System.err.println("Error checking database connection: " + e.getMessage());
            initializeDatabase();
        }
        return connection;
    }
    
    /**
     * إغلاق اتصال قاعدة البيانات
     * Close database connection
     */
    public static void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("تم إغلاق اتصال قاعدة البيانات");
                System.out.println("Database connection closed");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق اتصال قاعدة البيانات: " + e.getMessage());
            System.err.println("Error closing database connection: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * تنفيذ استعلام SQL
     * Execute SQL query
     * @param sql SQL statement to execute
     * @return true if successful, false otherwise
     */
    public static boolean executeUpdate(String sql) {
        try {
            Statement stmt = getConnection().createStatement();
            stmt.executeUpdate(sql);
            stmt.close();
            return true;
        } catch (SQLException e) {
            System.err.println("خطأ في تنفيذ الاستعلام: " + e.getMessage());
            System.err.println("Error executing query: " + e.getMessage());
            System.err.println("SQL: " + sql);
            e.printStackTrace();
            return false;
        }
    }
}
