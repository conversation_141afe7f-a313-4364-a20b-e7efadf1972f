package database;

/**
 * إدارة إنشاء جداول قاعدة البيانات
 * Database table creation and migrations
 */
public class Migrations {
    
    /**
     * إنشاء جميع الجداول المطلوبة
     * Create all required tables
     */
    public static void createTables() {
        createCustomersTable();
        createSuppliersTable();
        createProductsTable();
        createSalesTable();
        createSaleItemsTable();
        createPurchasesTable();
        createPurchaseItemsTable();
        
        System.out.println("تم إنشاء جميع الجداول بنجاح");
        System.out.println("All tables created successfully");
    }
    
    /**
     * إنشاء جدول العملاء
     * Create customers table
     */
    private static void createCustomersTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                debt_balance REAL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """;
        DBHelper.executeUpdate(sql);
    }
    
    /**
     * إنشاء جدول الموردين
     * Create suppliers table
     */
    private static void createSuppliersTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                payable_balance REAL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """;
        DBHelper.executeUpdate(sql);
    }
    
    /**
     * إنشاء جدول المنتجات
     * Create products table
     */
    private static void createProductsTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                purchase_price REAL NOT NULL DEFAULT 0.0,
                selling_price REAL NOT NULL DEFAULT 0.0,
                stock_quantity INTEGER DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """;
        DBHelper.executeUpdate(sql);
    }
    
    /**
     * إنشاء جدول المبيعات
     * Create sales table
     */
    private static void createSalesTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                total_amount REAL NOT NULL DEFAULT 0.0,
                paid_amount REAL DEFAULT 0.0,
                discount REAL DEFAULT 0.0,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """;
        DBHelper.executeUpdate(sql);
    }
    
    /**
     * إنشاء جدول تفاصيل المبيعات
     * Create sale items table
     */
    private static void createSaleItemsTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """;
        DBHelper.executeUpdate(sql);
    }
    
    /**
     * إنشاء جدول المشتريات
     * Create purchases table
     */
    private static void createPurchasesTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER,
                purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                total_amount REAL NOT NULL DEFAULT 0.0,
                paid_amount REAL DEFAULT 0.0,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        """;
        DBHelper.executeUpdate(sql);
    }
    
    /**
     * إنشاء جدول تفاصيل المشتريات
     * Create purchase items table
     */
    private static void createPurchaseItemsTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS purchase_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (purchase_id) REFERENCES purchases (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """;
        DBHelper.executeUpdate(sql);
    }
}
