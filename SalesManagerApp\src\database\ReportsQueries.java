package database;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * استعلامات التقارير والإحصائيات
 * Reports and statistics queries
 */
public class ReportsQueries {
    
    /**
     * حساب إجمالي المبيعات لفترة معينة
     * Calculate total sales for a specific period
     * @param startDate Start date (YYYY-MM-DD format)
     * @param endDate End date (YYYY-MM-DD format)
     * @return Total sales amount
     */
    public static double getTotalSales(String startDate, String endDate) {
        String sql = """
            SELECT COALESCE(SUM(total_amount), 0) as total_sales
            FROM sales 
            WHERE DATE(sale_date) BETWEEN ? AND ?
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, startDate);
            pstmt.setString(2, endDate);
            
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return rs.getDouble("total_sales");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في حساب إجمالي المبيعات: " + e.getMessage());
            System.err.println("Error calculating total sales: " + e.getMessage());
            e.printStackTrace();
        }
        return 0.0;
    }
    
    /**
     * حساب إجمالي الأرباح لفترة معينة
     * Calculate total profit for a specific period
     * @param startDate Start date
     * @param endDate End date
     * @return Total profit amount
     */
    public static double getTotalProfit(String startDate, String endDate) {
        String sql = """
            SELECT COALESCE(SUM(
                (si.unit_price - p.purchase_price) * si.quantity
            ), 0) as total_profit
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            JOIN products p ON si.product_id = p.id
            WHERE DATE(s.sale_date) BETWEEN ? AND ?
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, startDate);
            pstmt.setString(2, endDate);
            
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return rs.getDouble("total_profit");
            }
        } catch (SQLException e) {
            System.err.println("خطأ في حساب إجمالي الأرباح: " + e.getMessage());
            System.err.println("Error calculating total profit: " + e.getMessage());
            e.printStackTrace();
        }
        return 0.0;
    }
    
    /**
     * الحصول على أفضل العملاء (حسب المبيعات)
     * Get top customers by sales
     * @param limit Number of customers to return
     * @return List of customer data maps
     */
    public static List<Map<String, Object>> getTopCustomers(int limit) {
        String sql = """
            SELECT c.id, c.name, COALESCE(SUM(s.total_amount), 0) as total_purchases
            FROM customers c
            LEFT JOIN sales s ON c.id = s.customer_id
            GROUP BY c.id, c.name
            ORDER BY total_purchases DESC
            LIMIT ?
        """;
        
        List<Map<String, Object>> customers = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, limit);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> customer = new HashMap<>();
                customer.put("id", rs.getInt("id"));
                customer.put("name", rs.getString("name"));
                customer.put("total_purchases", rs.getDouble("total_purchases"));
                customers.add(customer);
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على أفضل العملاء: " + e.getMessage());
            System.err.println("Error getting top customers: " + e.getMessage());
            e.printStackTrace();
        }
        
        return customers;
    }
    
    /**
     * الحصول على المنتجات الأكثر مبيعاً
     * Get best selling products
     * @param limit Number of products to return
     * @return List of product data maps
     */
    public static List<Map<String, Object>> getBestSellingProducts(int limit) {
        String sql = """
            SELECT p.id, p.name, COALESCE(SUM(si.quantity), 0) as total_sold
            FROM products p
            LEFT JOIN sale_items si ON p.id = si.product_id
            GROUP BY p.id, p.name
            ORDER BY total_sold DESC
            LIMIT ?
        """;
        
        List<Map<String, Object>> products = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, limit);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> product = new HashMap<>();
                product.put("id", rs.getInt("id"));
                product.put("name", rs.getString("name"));
                product.put("total_sold", rs.getInt("total_sold"));
                products.add(product);
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على المنتجات الأكثر مبيعاً: " + e.getMessage());
            System.err.println("Error getting best selling products: " + e.getMessage());
            e.printStackTrace();
        }
        
        return products;
    }
    
    /**
     * الحصول على المنتجات منخفضة المخزون
     * Get low stock products
     * @return List of low stock products
     */
    public static List<Map<String, Object>> getLowStockProducts() {
        String sql = """
            SELECT id, name, stock_quantity, min_stock_level
            FROM products
            WHERE stock_quantity <= min_stock_level
            ORDER BY stock_quantity ASC
        """;
        
        List<Map<String, Object>> products = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> product = new HashMap<>();
                product.put("id", rs.getInt("id"));
                product.put("name", rs.getString("name"));
                product.put("stock_quantity", rs.getInt("stock_quantity"));
                product.put("min_stock_level", rs.getInt("min_stock_level"));
                products.add(product);
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على المنتجات منخفضة المخزون: " + e.getMessage());
            System.err.println("Error getting low stock products: " + e.getMessage());
            e.printStackTrace();
        }
        
        return products;
    }
}
