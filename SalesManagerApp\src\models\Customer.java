package models;

import database.DBHelper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج العميل
 * Customer model class
 */
public class Customer {
    private int id;
    private String name;
    private String phone;
    private String email;
    private String address;
    private double debtBalance;
    private String createdAt;
    private String updatedAt;
    
    // Constructors
    public Customer() {}
    
    public Customer(String name, String phone, String email, String address) {
        this.name = name;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.debtBalance = 0.0;
    }
    
    public Customer(int id, String name, String phone, String email, String address, 
                   double debtBalance, String createdAt, String updatedAt) {
        this.id = id;
        this.name = name;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.debtBalance = debtBalance;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public double getDebtBalance() { return debtBalance; }
    public void setDebtBalance(double debtBalance) { this.debtBalance = debtBalance; }
    
    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    
    public String getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }
    
    // Database operations
    
    /**
     * حفظ العميل في قاعدة البيانات
     * Save customer to database
     * @return true if successful, false otherwise
     */
    public boolean save() {
        if (id == 0) {
            return insert();
        } else {
            return update();
        }
    }
    
    /**
     * إدراج عميل جديد
     * Insert new customer
     */
    private boolean insert() {
        String sql = """
            INSERT INTO customers (name, phone, email, address, debt_balance)
            VALUES (?, ?, ?, ?, ?)
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, name);
            pstmt.setString(2, phone);
            pstmt.setString(3, email);
            pstmt.setString(4, address);
            pstmt.setDouble(5, debtBalance);
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                ResultSet generatedKeys = pstmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    this.id = generatedKeys.getInt(1);
                }
                return true;
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إدراج العميل: " + e.getMessage());
            System.err.println("Error inserting customer: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * تحديث بيانات العميل
     * Update customer data
     */
    private boolean update() {
        String sql = """
            UPDATE customers 
            SET name = ?, phone = ?, email = ?, address = ?, debt_balance = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, name);
            pstmt.setString(2, phone);
            pstmt.setString(3, email);
            pstmt.setString(4, address);
            pstmt.setDouble(5, debtBalance);
            pstmt.setInt(6, id);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("خطأ في تحديث العميل: " + e.getMessage());
            System.err.println("Error updating customer: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * حذف العميل
     * Delete customer
     * @return true if successful, false otherwise
     */
    public boolean delete() {
        String sql = "DELETE FROM customers WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("خطأ في حذف العميل: " + e.getMessage());
            System.err.println("Error deleting customer: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * البحث عن عميل بالمعرف
     * Find customer by ID
     * @param id Customer ID
     * @return Customer object or null if not found
     */
    public static Customer findById(int id) {
        String sql = "SELECT * FROM customers WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return new Customer(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address"),
                    rs.getDouble("debt_balance"),
                    rs.getString("created_at"),
                    rs.getString("updated_at")
                );
            }
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن العميل: " + e.getMessage());
            System.err.println("Error finding customer: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * الحصول على جميع العملاء
     * Get all customers
     * @return List of all customers
     */
    public static List<Customer> findAll() {
        String sql = "SELECT * FROM customers ORDER BY name";
        List<Customer> customers = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                customers.add(new Customer(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address"),
                    rs.getDouble("debt_balance"),
                    rs.getString("created_at"),
                    rs.getString("updated_at")
                ));
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على العملاء: " + e.getMessage());
            System.err.println("Error getting customers: " + e.getMessage());
            e.printStackTrace();
        }
        return customers;
    }
    
    @Override
    public String toString() {
        return name + " (ID: " + id + ")";
    }
}
