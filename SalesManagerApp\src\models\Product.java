package models;

import database.DBHelper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج المنتج
 * Product model class
 */
public class Product {
    private int id;
    private String name;
    private String description;
    private String category;
    private double purchasePrice;
    private double sellingPrice;
    private int stockQuantity;
    private int minStockLevel;
    private String createdAt;
    private String updatedAt;
    
    // Constructors
    public Product() {}
    
    public Product(String name, String description, String category, 
                  double purchasePrice, double sellingPrice, int stockQuantity, int minStockLevel) {
        this.name = name;
        this.description = description;
        this.category = category;
        this.purchasePrice = purchasePrice;
        this.sellingPrice = sellingPrice;
        this.stockQuantity = stockQuantity;
        this.minStockLevel = minStockLevel;
    }
    
    public Product(int id, String name, String description, String category, 
                  double purchasePrice, double sellingPrice, int stockQuantity, 
                  int minStockLevel, String createdAt, String updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.category = category;
        this.purchasePrice = purchasePrice;
        this.sellingPrice = sellingPrice;
        this.stockQuantity = stockQuantity;
        this.minStockLevel = minStockLevel;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public double getPurchasePrice() { return purchasePrice; }
    public void setPurchasePrice(double purchasePrice) { this.purchasePrice = purchasePrice; }
    
    public double getSellingPrice() { return sellingPrice; }
    public void setSellingPrice(double sellingPrice) { this.sellingPrice = sellingPrice; }
    
    public int getStockQuantity() { return stockQuantity; }
    public void setStockQuantity(int stockQuantity) { this.stockQuantity = stockQuantity; }
    
    public int getMinStockLevel() { return minStockLevel; }
    public void setMinStockLevel(int minStockLevel) { this.minStockLevel = minStockLevel; }
    
    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    
    public String getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }
    
    // Calculated properties
    public double getProfitMargin() {
        if (purchasePrice == 0) return 0;
        return ((sellingPrice - purchasePrice) / purchasePrice) * 100;
    }
    
    public boolean isLowStock() {
        return stockQuantity <= minStockLevel;
    }
    
    // Database operations
    
    /**
     * حفظ المنتج في قاعدة البيانات
     * Save product to database
     * @return true if successful, false otherwise
     */
    public boolean save() {
        if (id == 0) {
            return insert();
        } else {
            return update();
        }
    }
    
    /**
     * إدراج منتج جديد
     * Insert new product
     */
    private boolean insert() {
        String sql = """
            INSERT INTO products (name, description, category, purchase_price, 
                                selling_price, stock_quantity, min_stock_level)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, name);
            pstmt.setString(2, description);
            pstmt.setString(3, category);
            pstmt.setDouble(4, purchasePrice);
            pstmt.setDouble(5, sellingPrice);
            pstmt.setInt(6, stockQuantity);
            pstmt.setInt(7, minStockLevel);
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                ResultSet generatedKeys = pstmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    this.id = generatedKeys.getInt(1);
                }
                return true;
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إدراج المنتج: " + e.getMessage());
            System.err.println("Error inserting product: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * تحديث بيانات المنتج
     * Update product data
     */
    private boolean update() {
        String sql = """
            UPDATE products 
            SET name = ?, description = ?, category = ?, purchase_price = ?, 
                selling_price = ?, stock_quantity = ?, min_stock_level = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, name);
            pstmt.setString(2, description);
            pstmt.setString(3, category);
            pstmt.setDouble(4, purchasePrice);
            pstmt.setDouble(5, sellingPrice);
            pstmt.setInt(6, stockQuantity);
            pstmt.setInt(7, minStockLevel);
            pstmt.setInt(8, id);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("خطأ في تحديث المنتج: " + e.getMessage());
            System.err.println("Error updating product: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * حذف المنتج
     * Delete product
     * @return true if successful, false otherwise
     */
    public boolean delete() {
        String sql = "DELETE FROM products WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("خطأ في حذف المنتج: " + e.getMessage());
            System.err.println("Error deleting product: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * البحث عن منتج بالمعرف
     * Find product by ID
     * @param id Product ID
     * @return Product object or null if not found
     */
    public static Product findById(int id) {
        String sql = "SELECT * FROM products WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return new Product(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("description"),
                    rs.getString("category"),
                    rs.getDouble("purchase_price"),
                    rs.getDouble("selling_price"),
                    rs.getInt("stock_quantity"),
                    rs.getInt("min_stock_level"),
                    rs.getString("created_at"),
                    rs.getString("updated_at")
                );
            }
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن المنتج: " + e.getMessage());
            System.err.println("Error finding product: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * الحصول على جميع المنتجات
     * Get all products
     * @return List of all products
     */
    public static List<Product> findAll() {
        String sql = "SELECT * FROM products ORDER BY name";
        List<Product> products = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                products.add(new Product(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("description"),
                    rs.getString("category"),
                    rs.getDouble("purchase_price"),
                    rs.getDouble("selling_price"),
                    rs.getInt("stock_quantity"),
                    rs.getInt("min_stock_level"),
                    rs.getString("created_at"),
                    rs.getString("updated_at")
                ));
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على المنتجات: " + e.getMessage());
            System.err.println("Error getting products: " + e.getMessage());
            e.printStackTrace();
        }
        return products;
    }
    
    @Override
    public String toString() {
        return name + " (ID: " + id + ", Stock: " + stockQuantity + ")";
    }
}
