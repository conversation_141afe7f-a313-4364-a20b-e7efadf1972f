package models;

import database.DBHelper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج المشتريات
 * Purchase model class
 */
public class Purchase {
    private int id;
    private int supplierId;
    private String purchaseDate;
    private double totalAmount;
    private double paidAmount;
    private String notes;
    private String createdAt;
    private List<PurchaseItem> purchaseItems;
    
    // Constructors
    public Purchase() {
        this.purchaseItems = new ArrayList<>();
    }
    
    public Purchase(int supplierId, double totalAmount, double paidAmount, String notes) {
        this.supplierId = supplierId;
        this.totalAmount = totalAmount;
        this.paidAmount = paidAmount;
        this.notes = notes;
        this.purchaseItems = new ArrayList<>();
    }
    
    public Purchase(int id, int supplierId, String purchaseDate, double totalAmount, 
                   double paidAmount, String notes, String createdAt) {
        this.id = id;
        this.supplierId = supplierId;
        this.purchaseDate = purchaseDate;
        this.totalAmount = totalAmount;
        this.paidAmount = paidAmount;
        this.notes = notes;
        this.createdAt = createdAt;
        this.purchaseItems = new ArrayList<>();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getSupplierId() { return supplierId; }
    public void setSupplierId(int supplierId) { this.supplierId = supplierId; }
    
    public String getPurchaseDate() { return purchaseDate; }
    public void setPurchaseDate(String purchaseDate) { this.purchaseDate = purchaseDate; }
    
    public double getTotalAmount() { return totalAmount; }
    public void setTotalAmount(double totalAmount) { this.totalAmount = totalAmount; }
    
    public double getPaidAmount() { return paidAmount; }
    public void setPaidAmount(double paidAmount) { this.paidAmount = paidAmount; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    
    public List<PurchaseItem> getPurchaseItems() { return purchaseItems; }
    public void setPurchaseItems(List<PurchaseItem> purchaseItems) { this.purchaseItems = purchaseItems; }
    
    // Calculated properties
    public double getRemainingAmount() {
        return totalAmount - paidAmount;
    }
    
    public boolean isFullyPaid() {
        return paidAmount >= totalAmount;
    }
    
    // Helper methods
    public void addPurchaseItem(PurchaseItem item) {
        purchaseItems.add(item);
        recalculateTotal();
    }
    
    public void removePurchaseItem(PurchaseItem item) {
        purchaseItems.remove(item);
        recalculateTotal();
    }
    
    private void recalculateTotal() {
        totalAmount = purchaseItems.stream()
                                  .mapToDouble(PurchaseItem::getTotalPrice)
                                  .sum();
    }
    
    // Database operations
    
    /**
     * حفظ المشتريات في قاعدة البيانات
     * Save purchase to database
     * @return true if successful, false otherwise
     */
    public boolean save() {
        Connection conn = null;
        try {
            conn = DBHelper.getConnection();
            conn.setAutoCommit(false); // Start transaction
            
            boolean success;
            if (id == 0) {
                success = insert(conn);
            } else {
                success = update(conn);
            }
            
            if (success) {
                // Save purchase items
                success = savePurchaseItems(conn);
            }
            
            if (success) {
                conn.commit();
                return true;
            } else {
                conn.rollback();
                return false;
            }
        } catch (SQLException e) {
            try {
                if (conn != null) conn.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            System.err.println("خطأ في حفظ المشتريات: " + e.getMessage());
            System.err.println("Error saving purchase: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (conn != null) {
                    conn.setAutoCommit(true);
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * إدراج مشتريات جديدة
     * Insert new purchase
     */
    private boolean insert(Connection conn) throws SQLException {
        String sql = """
            INSERT INTO purchases (supplier_id, total_amount, paid_amount, notes)
            VALUES (?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            pstmt.setInt(1, supplierId);
            pstmt.setDouble(2, totalAmount);
            pstmt.setDouble(3, paidAmount);
            pstmt.setString(4, notes);
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                ResultSet generatedKeys = pstmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    this.id = generatedKeys.getInt(1);
                }
                return true;
            }
        }
        return false;
    }
    
    /**
     * تحديث بيانات المشتريات
     * Update purchase data
     */
    private boolean update(Connection conn) throws SQLException {
        String sql = """
            UPDATE purchases 
            SET supplier_id = ?, total_amount = ?, paid_amount = ?, notes = ?
            WHERE id = ?
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, supplierId);
            pstmt.setDouble(2, totalAmount);
            pstmt.setDouble(3, paidAmount);
            pstmt.setString(4, notes);
            pstmt.setInt(5, id);
            
            return pstmt.executeUpdate() > 0;
        }
    }
    
    /**
     * حفظ عناصر المشتريات
     * Save purchase items
     */
    private boolean savePurchaseItems(Connection conn) throws SQLException {
        // Delete existing items
        String deleteSql = "DELETE FROM purchase_items WHERE purchase_id = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(deleteSql)) {
            pstmt.setInt(1, id);
            pstmt.executeUpdate();
        }
        
        // Insert new items
        String insertSql = """
            INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_price, total_price)
            VALUES (?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(insertSql)) {
            for (PurchaseItem item : purchaseItems) {
                pstmt.setInt(1, id);
                pstmt.setInt(2, item.getProductId());
                pstmt.setInt(3, item.getQuantity());
                pstmt.setDouble(4, item.getUnitPrice());
                pstmt.setDouble(5, item.getTotalPrice());
                pstmt.addBatch();
            }
            pstmt.executeBatch();
        }
        
        return true;
    }
    
    /**
     * البحث عن مشتريات بالمعرف
     * Find purchase by ID
     * @param id Purchase ID
     * @return Purchase object or null if not found
     */
    public static Purchase findById(int id) {
        String sql = "SELECT * FROM purchases WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                Purchase purchase = new Purchase(
                    rs.getInt("id"),
                    rs.getInt("supplier_id"),
                    rs.getString("purchase_date"),
                    rs.getDouble("total_amount"),
                    rs.getDouble("paid_amount"),
                    rs.getString("notes"),
                    rs.getString("created_at")
                );
                
                // Load purchase items
                purchase.setPurchaseItems(PurchaseItem.findByPurchaseId(id));
                return purchase;
            }
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن المشتريات: " + e.getMessage());
            System.err.println("Error finding purchase: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * الحصول على جميع المشتريات
     * Get all purchases
     * @return List of all purchases
     */
    public static List<Purchase> findAll() {
        String sql = "SELECT * FROM purchases ORDER BY purchase_date DESC";
        List<Purchase> purchases = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                purchases.add(new Purchase(
                    rs.getInt("id"),
                    rs.getInt("supplier_id"),
                    rs.getString("purchase_date"),
                    rs.getDouble("total_amount"),
                    rs.getDouble("paid_amount"),
                    rs.getString("notes"),
                    rs.getString("created_at")
                ));
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على المشتريات: " + e.getMessage());
            System.err.println("Error getting purchases: " + e.getMessage());
            e.printStackTrace();
        }
        return purchases;
    }
    
    @Override
    public String toString() {
        return "Purchase ID: " + id + " - Total: " + totalAmount;
    }
}
