package models;

import database.DBHelper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج عنصر المشتريات
 * Purchase item model class
 */
public class PurchaseItem {
    private int id;
    private int purchaseId;
    private int productId;
    private int quantity;
    private double unitPrice;
    private double totalPrice;
    
    // Constructors
    public PurchaseItem() {}
    
    public PurchaseItem(int productId, int quantity, double unitPrice) {
        this.productId = productId;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
    }
    
    public PurchaseItem(int id, int purchaseId, int productId, int quantity, double unitPrice, double totalPrice) {
        this.id = id;
        this.purchaseId = purchaseId;
        this.productId = productId;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = totalPrice;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getPurchaseId() { return purchaseId; }
    public void setPurchaseId(int purchaseId) { this.purchaseId = purchaseId; }
    
    public int getProductId() { return productId; }
    public void setProductId(int productId) { this.productId = productId; }
    
    public int getQuantity() { return quantity; }
    public void setQuantity(int quantity) { 
        this.quantity = quantity;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { 
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getTotalPrice() { return totalPrice; }
    public void setTotalPrice(double totalPrice) { this.totalPrice = totalPrice; }
    
    // Helper methods
    public void recalculateTotal() {
        this.totalPrice = quantity * unitPrice;
    }
    
    // Database operations
    
    /**
     * البحث عن عناصر المشتريات بمعرف المشتريات
     * Find purchase items by purchase ID
     * @param purchaseId Purchase ID
     * @return List of purchase items
     */
    public static List<PurchaseItem> findByPurchaseId(int purchaseId) {
        String sql = "SELECT * FROM purchase_items WHERE purchase_id = ?";
        List<PurchaseItem> items = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, purchaseId);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                items.add(new PurchaseItem(
                    rs.getInt("id"),
                    rs.getInt("purchase_id"),
                    rs.getInt("product_id"),
                    rs.getInt("quantity"),
                    rs.getDouble("unit_price"),
                    rs.getDouble("total_price")
                ));
            }
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن عناصر المشتريات: " + e.getMessage());
            System.err.println("Error finding purchase items: " + e.getMessage());
            e.printStackTrace();
        }
        return items;
    }
    
    /**
     * حذف عناصر المشتريات بمعرف المشتريات
     * Delete purchase items by purchase ID
     * @param purchaseId Purchase ID
     * @return true if successful, false otherwise
     */
    public static boolean deleteByPurchaseId(int purchaseId) {
        String sql = "DELETE FROM purchase_items WHERE purchase_id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, purchaseId);
            return pstmt.executeUpdate() >= 0; // Returns true even if no rows affected
        } catch (SQLException e) {
            System.err.println("خطأ في حذف عناصر المشتريات: " + e.getMessage());
            System.err.println("Error deleting purchase items: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public String toString() {
        return "Product ID: " + productId + " - Qty: " + quantity + " - Total: " + totalPrice;
    }
}
