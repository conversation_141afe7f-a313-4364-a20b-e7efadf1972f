package models;

import database.DBHelper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج المبيعة (الفاتورة)
 * Sale model class
 */
public class Sale {
    private int id;
    private int customerId;
    private String saleDate;
    private double totalAmount;
    private double paidAmount;
    private double discount;
    private String notes;
    private String createdAt;
    private List<SaleItem> saleItems;
    
    // Constructors
    public Sale() {
        this.saleItems = new ArrayList<>();
    }
    
    public Sale(int customerId, double totalAmount, double paidAmount, double discount, String notes) {
        this.customerId = customerId;
        this.totalAmount = totalAmount;
        this.paidAmount = paidAmount;
        this.discount = discount;
        this.notes = notes;
        this.saleItems = new ArrayList<>();
    }
    
    public Sale(int id, int customerId, String saleDate, double totalAmount, 
               double paidAmount, double discount, String notes, String createdAt) {
        this.id = id;
        this.customerId = customerId;
        this.saleDate = saleDate;
        this.totalAmount = totalAmount;
        this.paidAmount = paidAmount;
        this.discount = discount;
        this.notes = notes;
        this.createdAt = createdAt;
        this.saleItems = new ArrayList<>();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getCustomerId() { return customerId; }
    public void setCustomerId(int customerId) { this.customerId = customerId; }
    
    public String getSaleDate() { return saleDate; }
    public void setSaleDate(String saleDate) { this.saleDate = saleDate; }
    
    public double getTotalAmount() { return totalAmount; }
    public void setTotalAmount(double totalAmount) { this.totalAmount = totalAmount; }
    
    public double getPaidAmount() { return paidAmount; }
    public void setPaidAmount(double paidAmount) { this.paidAmount = paidAmount; }
    
    public double getDiscount() { return discount; }
    public void setDiscount(double discount) { this.discount = discount; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    
    public List<SaleItem> getSaleItems() { return saleItems; }
    public void setSaleItems(List<SaleItem> saleItems) { this.saleItems = saleItems; }
    
    // Calculated properties
    public double getRemainingAmount() {
        return totalAmount - paidAmount;
    }
    
    public boolean isFullyPaid() {
        return paidAmount >= totalAmount;
    }
    
    // Helper methods
    public void addSaleItem(SaleItem item) {
        saleItems.add(item);
        recalculateTotal();
    }
    
    public void removeSaleItem(SaleItem item) {
        saleItems.remove(item);
        recalculateTotal();
    }
    
    private void recalculateTotal() {
        totalAmount = saleItems.stream()
                              .mapToDouble(SaleItem::getTotalPrice)
                              .sum() - discount;
    }
    
    // Database operations
    
    /**
     * حفظ المبيعة في قاعدة البيانات
     * Save sale to database
     * @return true if successful, false otherwise
     */
    public boolean save() {
        Connection conn = null;
        try {
            conn = DBHelper.getConnection();
            conn.setAutoCommit(false); // Start transaction
            
            boolean success;
            if (id == 0) {
                success = insert(conn);
            } else {
                success = update(conn);
            }
            
            if (success) {
                // Save sale items
                success = saveSaleItems(conn);
            }
            
            if (success) {
                conn.commit();
                return true;
            } else {
                conn.rollback();
                return false;
            }
        } catch (SQLException e) {
            try {
                if (conn != null) conn.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            System.err.println("خطأ في حفظ المبيعة: " + e.getMessage());
            System.err.println("Error saving sale: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (conn != null) {
                    conn.setAutoCommit(true);
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * إدراج مبيعة جديدة
     * Insert new sale
     */
    private boolean insert(Connection conn) throws SQLException {
        String sql = """
            INSERT INTO sales (customer_id, total_amount, paid_amount, discount, notes)
            VALUES (?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            pstmt.setInt(1, customerId);
            pstmt.setDouble(2, totalAmount);
            pstmt.setDouble(3, paidAmount);
            pstmt.setDouble(4, discount);
            pstmt.setString(5, notes);
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                ResultSet generatedKeys = pstmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    this.id = generatedKeys.getInt(1);
                }
                return true;
            }
        }
        return false;
    }
    
    /**
     * تحديث بيانات المبيعة
     * Update sale data
     */
    private boolean update(Connection conn) throws SQLException {
        String sql = """
            UPDATE sales 
            SET customer_id = ?, total_amount = ?, paid_amount = ?, discount = ?, notes = ?
            WHERE id = ?
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, customerId);
            pstmt.setDouble(2, totalAmount);
            pstmt.setDouble(3, paidAmount);
            pstmt.setDouble(4, discount);
            pstmt.setString(5, notes);
            pstmt.setInt(6, id);
            
            return pstmt.executeUpdate() > 0;
        }
    }
    
    /**
     * حفظ عناصر المبيعة
     * Save sale items
     */
    private boolean saveSaleItems(Connection conn) throws SQLException {
        // Delete existing items
        String deleteSql = "DELETE FROM sale_items WHERE sale_id = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(deleteSql)) {
            pstmt.setInt(1, id);
            pstmt.executeUpdate();
        }
        
        // Insert new items
        String insertSql = """
            INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price)
            VALUES (?, ?, ?, ?, ?)
        """;
        
        try (PreparedStatement pstmt = conn.prepareStatement(insertSql)) {
            for (SaleItem item : saleItems) {
                pstmt.setInt(1, id);
                pstmt.setInt(2, item.getProductId());
                pstmt.setInt(3, item.getQuantity());
                pstmt.setDouble(4, item.getUnitPrice());
                pstmt.setDouble(5, item.getTotalPrice());
                pstmt.addBatch();
            }
            pstmt.executeBatch();
        }
        
        return true;
    }
    
    /**
     * البحث عن مبيعة بالمعرف
     * Find sale by ID
     * @param id Sale ID
     * @return Sale object or null if not found
     */
    public static Sale findById(int id) {
        String sql = "SELECT * FROM sales WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                Sale sale = new Sale(
                    rs.getInt("id"),
                    rs.getInt("customer_id"),
                    rs.getString("sale_date"),
                    rs.getDouble("total_amount"),
                    rs.getDouble("paid_amount"),
                    rs.getDouble("discount"),
                    rs.getString("notes"),
                    rs.getString("created_at")
                );
                
                // Load sale items
                sale.setSaleItems(SaleItem.findBySaleId(id));
                return sale;
            }
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن المبيعة: " + e.getMessage());
            System.err.println("Error finding sale: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * الحصول على جميع المبيعات
     * Get all sales
     * @return List of all sales
     */
    public static List<Sale> findAll() {
        String sql = "SELECT * FROM sales ORDER BY sale_date DESC";
        List<Sale> sales = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                sales.add(new Sale(
                    rs.getInt("id"),
                    rs.getInt("customer_id"),
                    rs.getString("sale_date"),
                    rs.getDouble("total_amount"),
                    rs.getDouble("paid_amount"),
                    rs.getDouble("discount"),
                    rs.getString("notes"),
                    rs.getString("created_at")
                ));
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على المبيعات: " + e.getMessage());
            System.err.println("Error getting sales: " + e.getMessage());
            e.printStackTrace();
        }
        return sales;
    }
    
    @Override
    public String toString() {
        return "Sale ID: " + id + " - Total: " + totalAmount;
    }
}
