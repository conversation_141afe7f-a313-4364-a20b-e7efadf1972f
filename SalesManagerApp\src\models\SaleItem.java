package models;

import database.DBHelper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج عنصر المبيعة
 * Sale item model class
 */
public class SaleItem {
    private int id;
    private int saleId;
    private int productId;
    private int quantity;
    private double unitPrice;
    private double totalPrice;
    
    // Constructors
    public SaleItem() {}
    
    public SaleItem(int productId, int quantity, double unitPrice) {
        this.productId = productId;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
    }
    
    public SaleItem(int id, int saleId, int productId, int quantity, double unitPrice, double totalPrice) {
        this.id = id;
        this.saleId = saleId;
        this.productId = productId;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = totalPrice;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getSaleId() { return saleId; }
    public void setSaleId(int saleId) { this.saleId = saleId; }
    
    public int getProductId() { return productId; }
    public void setProductId(int productId) { this.productId = productId; }
    
    public int getQuantity() { return quantity; }
    public void setQuantity(int quantity) { 
        this.quantity = quantity;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { 
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getTotalPrice() { return totalPrice; }
    public void setTotalPrice(double totalPrice) { this.totalPrice = totalPrice; }
    
    // Helper methods
    public void recalculateTotal() {
        this.totalPrice = quantity * unitPrice;
    }
    
    // Database operations
    
    /**
     * البحث عن عناصر المبيعة بمعرف المبيعة
     * Find sale items by sale ID
     * @param saleId Sale ID
     * @return List of sale items
     */
    public static List<SaleItem> findBySaleId(int saleId) {
        String sql = "SELECT * FROM sale_items WHERE sale_id = ?";
        List<SaleItem> items = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, saleId);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                items.add(new SaleItem(
                    rs.getInt("id"),
                    rs.getInt("sale_id"),
                    rs.getInt("product_id"),
                    rs.getInt("quantity"),
                    rs.getDouble("unit_price"),
                    rs.getDouble("total_price")
                ));
            }
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن عناصر المبيعة: " + e.getMessage());
            System.err.println("Error finding sale items: " + e.getMessage());
            e.printStackTrace();
        }
        return items;
    }
    
    /**
     * حذف عناصر المبيعة بمعرف المبيعة
     * Delete sale items by sale ID
     * @param saleId Sale ID
     * @return true if successful, false otherwise
     */
    public static boolean deleteBySaleId(int saleId) {
        String sql = "DELETE FROM sale_items WHERE sale_id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, saleId);
            return pstmt.executeUpdate() >= 0; // Returns true even if no rows affected
        } catch (SQLException e) {
            System.err.println("خطأ في حذف عناصر المبيعة: " + e.getMessage());
            System.err.println("Error deleting sale items: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    @Override
    public String toString() {
        return "Product ID: " + productId + " - Qty: " + quantity + " - Total: " + totalPrice;
    }
}
