package models;

import database.DBHelper;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج المورد
 * Supplier model class
 */
public class Supplier {
    private int id;
    private String name;
    private String phone;
    private String email;
    private String address;
    private double payableBalance;
    private String createdAt;
    private String updatedAt;
    
    // Constructors
    public Supplier() {}
    
    public Supplier(String name, String phone, String email, String address) {
        this.name = name;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.payableBalance = 0.0;
    }
    
    public Supplier(int id, String name, String phone, String email, String address, 
                   double payableBalance, String createdAt, String updatedAt) {
        this.id = id;
        this.name = name;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.payableBalance = payableBalance;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public double getPayableBalance() { return payableBalance; }
    public void setPayableBalance(double payableBalance) { this.payableBalance = payableBalance; }
    
    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    
    public String getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }
    
    // Database operations
    
    /**
     * حفظ المورد في قاعدة البيانات
     * Save supplier to database
     * @return true if successful, false otherwise
     */
    public boolean save() {
        if (id == 0) {
            return insert();
        } else {
            return update();
        }
    }
    
    /**
     * إدراج مورد جديد
     * Insert new supplier
     */
    private boolean insert() {
        String sql = """
            INSERT INTO suppliers (name, phone, email, address, payable_balance)
            VALUES (?, ?, ?, ?, ?)
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, name);
            pstmt.setString(2, phone);
            pstmt.setString(3, email);
            pstmt.setString(4, address);
            pstmt.setDouble(5, payableBalance);
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                ResultSet generatedKeys = pstmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    this.id = generatedKeys.getInt(1);
                }
                return true;
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إدراج المورد: " + e.getMessage());
            System.err.println("Error inserting supplier: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * تحديث بيانات المورد
     * Update supplier data
     */
    private boolean update() {
        String sql = """
            UPDATE suppliers 
            SET name = ?, phone = ?, email = ?, address = ?, payable_balance = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """;
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, name);
            pstmt.setString(2, phone);
            pstmt.setString(3, email);
            pstmt.setString(4, address);
            pstmt.setDouble(5, payableBalance);
            pstmt.setInt(6, id);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("خطأ في تحديث المورد: " + e.getMessage());
            System.err.println("Error updating supplier: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * حذف المورد
     * Delete supplier
     * @return true if successful, false otherwise
     */
    public boolean delete() {
        String sql = "DELETE FROM suppliers WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("خطأ في حذف المورد: " + e.getMessage());
            System.err.println("Error deleting supplier: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * البحث عن مورد بالمعرف
     * Find supplier by ID
     * @param id Supplier ID
     * @return Supplier object or null if not found
     */
    public static Supplier findById(int id) {
        String sql = "SELECT * FROM suppliers WHERE id = ?";
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return new Supplier(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address"),
                    rs.getDouble("payable_balance"),
                    rs.getString("created_at"),
                    rs.getString("updated_at")
                );
            }
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن المورد: " + e.getMessage());
            System.err.println("Error finding supplier: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * الحصول على جميع الموردين
     * Get all suppliers
     * @return List of all suppliers
     */
    public static List<Supplier> findAll() {
        String sql = "SELECT * FROM suppliers ORDER BY name";
        List<Supplier> suppliers = new ArrayList<>();
        
        try (Connection conn = DBHelper.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                suppliers.add(new Supplier(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address"),
                    rs.getDouble("payable_balance"),
                    rs.getString("created_at"),
                    rs.getString("updated_at")
                ));
            }
        } catch (SQLException e) {
            System.err.println("خطأ في الحصول على الموردين: " + e.getMessage());
            System.err.println("Error getting suppliers: " + e.getMessage());
            e.printStackTrace();
        }
        return suppliers;
    }
    
    @Override
    public String toString() {
        return name + " (ID: " + id + ")";
    }
}
