package ui;

import models.Customer;
import utils.Formatter;
import utils.Validator;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * صفحة إدارة العملاء
 * Customers management page
 */
public class CustomersPage extends JPanel {
    
    private JTable customersTable;
    private DefaultTableModel tableModel;
    private JTextField nameField, phoneField, emailField, addressField;
    private JButton addButton, editButton, deleteButton, refreshButton;
    private Customer selectedCustomer;
    
    public CustomersPage() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadCustomers();
    }
    
    /**
     * تهيئة المكونات
     * Initialize components
     */
    private void initializeComponents() {
        // Create table
        String[] columnNames = {"ID", "الاسم / Name", "الهاتف / Phone", "البريد / Email", 
                               "العنوان / Address", "الديون / Debt", "تاريخ الإنشاء / Created"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Make table read-only
            }
        };
        customersTable = new JTable(tableModel);
        customersTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        customersTable.setRowHeight(25);
        
        // Create form fields
        nameField = new JTextField(20);
        phoneField = new JTextField(20);
        emailField = new JTextField(20);
        addressField = new JTextField(20);
        
        // Create buttons
        addButton = new JButton("إضافة / Add");
        editButton = new JButton("تعديل / Edit");
        deleteButton = new JButton("حذف / Delete");
        refreshButton = new JButton("تحديث / Refresh");
        
        // Initially disable edit and delete buttons
        editButton.setEnabled(false);
        deleteButton.setEnabled(false);
    }
    
    /**
     * إعداد التخطيط
     * Setup layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Create form panel
        JPanel formPanel = createFormPanel();
        add(formPanel, BorderLayout.NORTH);
        
        // Create table panel
        JScrollPane tableScrollPane = new JScrollPane(customersTable);
        tableScrollPane.setBorder(BorderFactory.createTitledBorder("قائمة العملاء / Customers List"));
        add(tableScrollPane, BorderLayout.CENTER);
        
        // Create button panel
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء لوحة النموذج
     * Create form panel
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("بيانات العميل / Customer Information"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // Name field
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("الاسم / Name:"), gbc);
        gbc.gridx = 1;
        panel.add(nameField, gbc);
        
        // Phone field
        gbc.gridx = 2; gbc.gridy = 0;
        panel.add(new JLabel("الهاتف / Phone:"), gbc);
        gbc.gridx = 3;
        panel.add(phoneField, gbc);
        
        // Email field
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("البريد / Email:"), gbc);
        gbc.gridx = 1;
        panel.add(emailField, gbc);
        
        // Address field
        gbc.gridx = 2; gbc.gridy = 1;
        panel.add(new JLabel("العنوان / Address:"), gbc);
        gbc.gridx = 3;
        panel.add(addressField, gbc);
        
        return panel;
    }
    
    /**
     * إنشاء لوحة الأزرار
     * Create button panel
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        panel.add(addButton);
        panel.add(editButton);
        panel.add(deleteButton);
        panel.add(refreshButton);
        
        return panel;
    }
    
    /**
     * إعداد معالجات الأحداث
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Table selection listener
        customersTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = customersTable.getSelectedRow();
                if (selectedRow >= 0) {
                    loadSelectedCustomer(selectedRow);
                    editButton.setEnabled(true);
                    deleteButton.setEnabled(true);
                } else {
                    clearForm();
                    editButton.setEnabled(false);
                    deleteButton.setEnabled(false);
                }
            }
        });
        
        // Button listeners
        addButton.addActionListener(e -> addCustomer());
        editButton.addActionListener(e -> editCustomer());
        deleteButton.addActionListener(e -> deleteCustomer());
        refreshButton.addActionListener(e -> loadCustomers());
    }
    
    /**
     * تحميل العملاء من قاعدة البيانات
     * Load customers from database
     */
    private void loadCustomers() {
        try {
            List<Customer> customers = Customer.findAll();
            tableModel.setRowCount(0); // Clear existing data
            
            for (Customer customer : customers) {
                Object[] row = {
                    customer.getId(),
                    customer.getName(),
                    customer.getPhone(),
                    customer.getEmail(),
                    customer.getAddress(),
                    Formatter.formatCurrency(customer.getDebtBalance()),
                    customer.getCreatedAt()
                };
                tableModel.addRow(row);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "خطأ في تحميل العملاء: " + e.getMessage() + "\nError loading customers: " + e.getMessage(),
                "خطأ / Error", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تحميل العميل المحدد
     * Load selected customer
     */
    private void loadSelectedCustomer(int row) {
        try {
            int customerId = (Integer) tableModel.getValueAt(row, 0);
            selectedCustomer = Customer.findById(customerId);
            
            if (selectedCustomer != null) {
                nameField.setText(selectedCustomer.getName());
                phoneField.setText(selectedCustomer.getPhone());
                emailField.setText(selectedCustomer.getEmail());
                addressField.setText(selectedCustomer.getAddress());
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "خطأ في تحميل بيانات العميل: " + e.getMessage() + "\nError loading customer data: " + e.getMessage(),
                "خطأ / Error", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * إضافة عميل جديد
     * Add new customer
     */
    private void addCustomer() {
        if (!validateForm()) return;
        
        try {
            Customer customer = new Customer(
                nameField.getText().trim(),
                phoneField.getText().trim(),
                emailField.getText().trim(),
                addressField.getText().trim()
            );
            
            if (customer.save()) {
                JOptionPane.showMessageDialog(this, 
                    "تم إضافة العميل بنجاح\nCustomer added successfully",
                    "نجح / Success", 
                    JOptionPane.INFORMATION_MESSAGE);
                clearForm();
                loadCustomers();
            } else {
                JOptionPane.showMessageDialog(this, 
                    "فشل في إضافة العميل\nFailed to add customer",
                    "خطأ / Error", 
                    JOptionPane.ERROR_MESSAGE);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "خطأ في إضافة العميل: " + e.getMessage() + "\nError adding customer: " + e.getMessage(),
                "خطأ / Error", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تعديل العميل
     * Edit customer
     */
    private void editCustomer() {
        if (selectedCustomer == null || !validateForm()) return;
        
        try {
            selectedCustomer.setName(nameField.getText().trim());
            selectedCustomer.setPhone(phoneField.getText().trim());
            selectedCustomer.setEmail(emailField.getText().trim());
            selectedCustomer.setAddress(addressField.getText().trim());
            
            if (selectedCustomer.save()) {
                JOptionPane.showMessageDialog(this, 
                    "تم تحديث العميل بنجاح\nCustomer updated successfully",
                    "نجح / Success", 
                    JOptionPane.INFORMATION_MESSAGE);
                clearForm();
                loadCustomers();
            } else {
                JOptionPane.showMessageDialog(this, 
                    "فشل في تحديث العميل\nFailed to update customer",
                    "خطأ / Error", 
                    JOptionPane.ERROR_MESSAGE);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "خطأ في تحديث العميل: " + e.getMessage() + "\nError updating customer: " + e.getMessage(),
                "خطأ / Error", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * حذف العميل
     * Delete customer
     */
    private void deleteCustomer() {
        if (selectedCustomer == null) return;
        
        int option = JOptionPane.showConfirmDialog(this,
            "هل تريد حذف العميل؟\nDo you want to delete this customer?",
            "تأكيد الحذف / Confirm Delete",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE);
        
        if (option == JOptionPane.YES_OPTION) {
            try {
                if (selectedCustomer.delete()) {
                    JOptionPane.showMessageDialog(this, 
                        "تم حذف العميل بنجاح\nCustomer deleted successfully",
                        "نجح / Success", 
                        JOptionPane.INFORMATION_MESSAGE);
                    clearForm();
                    loadCustomers();
                } else {
                    JOptionPane.showMessageDialog(this, 
                        "فشل في حذف العميل\nFailed to delete customer",
                        "خطأ / Error", 
                        JOptionPane.ERROR_MESSAGE);
                }
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this, 
                    "خطأ في حذف العميل: " + e.getMessage() + "\nError deleting customer: " + e.getMessage(),
                    "خطأ / Error", 
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * التحقق من صحة النموذج
     * Validate form
     */
    private boolean validateForm() {
        String name = nameField.getText().trim();
        String phone = phoneField.getText().trim();
        String email = emailField.getText().trim();
        
        if (!Validator.isValidName(name)) {
            JOptionPane.showMessageDialog(this, Validator.getNameErrorMessage(), "خطأ / Error", JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }
        
        if (!phone.isEmpty() && !Validator.isValidPhone(phone)) {
            JOptionPane.showMessageDialog(this, Validator.getPhoneErrorMessage(), "خطأ / Error", JOptionPane.ERROR_MESSAGE);
            phoneField.requestFocus();
            return false;
        }
        
        if (!email.isEmpty() && !Validator.isValidEmail(email)) {
            JOptionPane.showMessageDialog(this, Validator.getEmailErrorMessage(), "خطأ / Error", JOptionPane.ERROR_MESSAGE);
            emailField.requestFocus();
            return false;
        }
        
        return true;
    }
    
    /**
     * مسح النموذج
     * Clear form
     */
    private void clearForm() {
        nameField.setText("");
        phoneField.setText("");
        emailField.setText("");
        addressField.setText("");
        selectedCustomer = null;
        customersTable.clearSelection();
    }
}
