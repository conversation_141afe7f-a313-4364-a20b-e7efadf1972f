package ui;

import database.DBHelper;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;

/**
 * النافذة الرئيسية للتطبيق
 * Main application window
 */
public class MainWindow extends JFrame {
    
    private JTabbedPane tabbedPane;
    private CustomersPage customersPage;
    private SuppliersPage suppliersPage;
    private ProductsPage productsPage;
    private SalesPage salesPage;
    private PurchasesPage purchasesPage;
    private ReportsPage reportsPage;
    
    public MainWindow() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        
        // Center window on screen
        setLocationRelativeTo(null);
    }
    
    /**
     * تهيئة المكونات
     * Initialize components
     */
    private void initializeComponents() {
        setTitle("نظام إدارة المبيعات - Sales Management System");
        setSize(1200, 800);
        setMinimumSize(new Dimension(1000, 600));
        
        // Create tabbed pane
        tabbedPane = new JTabbedPane(JTabbedPane.TOP);
        tabbedPane.setFont(new Font("Arial", Font.BOLD, 14));
        
        // Initialize pages
        customersPage = new CustomersPage();
        suppliersPage = new SuppliersPage();
        productsPage = new ProductsPage();
        salesPage = new SalesPage();
        purchasesPage = new PurchasesPage();
        reportsPage = new ReportsPage();
        
        // Add tabs
        tabbedPane.addTab("العملاء / Customers", createTabIcon("👥"), customersPage, "إدارة العملاء والديون");
        tabbedPane.addTab("الموردين / Suppliers", createTabIcon("🏢"), suppliersPage, "إدارة الموردين والمستحقات");
        tabbedPane.addTab("المنتجات / Products", createTabIcon("📦"), productsPage, "إدارة المخزون والأسعار");
        tabbedPane.addTab("المبيعات / Sales", createTabIcon("💰"), salesPage, "إصدار فواتير البيع");
        tabbedPane.addTab("المشتريات / Purchases", createTabIcon("🛒"), purchasesPage, "إدارة فواتير الشراء");
        tabbedPane.addTab("التقارير / Reports", createTabIcon("📊"), reportsPage, "التقارير والإحصائيات");
    }
    
    /**
     * إعداد التخطيط
     * Setup layout
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // Create menu bar
        JMenuBar menuBar = createMenuBar();
        setJMenuBar(menuBar);
        
        // Create toolbar
        JToolBar toolBar = createToolBar();
        add(toolBar, BorderLayout.NORTH);
        
        // Add main content
        add(tabbedPane, BorderLayout.CENTER);
        
        // Create status bar
        JPanel statusBar = createStatusBar();
        add(statusBar, BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء شريط القوائم
     * Create menu bar
     */
    private JMenuBar createMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        
        // File menu
        JMenu fileMenu = new JMenu("ملف / File");
        fileMenu.add(createMenuItem("جديد / New", "Ctrl+N", e -> newDocument()));
        fileMenu.add(createMenuItem("فتح / Open", "Ctrl+O", e -> openDocument()));
        fileMenu.add(createMenuItem("حفظ / Save", "Ctrl+S", e -> saveDocument()));
        fileMenu.addSeparator();
        fileMenu.add(createMenuItem("تصدير / Export", "Ctrl+E", e -> exportData()));
        fileMenu.addSeparator();
        fileMenu.add(createMenuItem("خروج / Exit", "Alt+F4", e -> exitApplication()));
        
        // Edit menu
        JMenu editMenu = new JMenu("تحرير / Edit");
        editMenu.add(createMenuItem("تراجع / Undo", "Ctrl+Z", e -> undo()));
        editMenu.add(createMenuItem("إعادة / Redo", "Ctrl+Y", e -> redo()));
        editMenu.addSeparator();
        editMenu.add(createMenuItem("نسخ / Copy", "Ctrl+C", e -> copy()));
        editMenu.add(createMenuItem("لصق / Paste", "Ctrl+V", e -> paste()));
        
        // View menu
        JMenu viewMenu = new JMenu("عرض / View");
        viewMenu.add(createMenuItem("تحديث / Refresh", "F5", e -> refreshCurrentTab()));
        viewMenu.add(createMenuItem("ملء الشاشة / Full Screen", "F11", e -> toggleFullScreen()));
        
        // Tools menu
        JMenu toolsMenu = new JMenu("أدوات / Tools");
        toolsMenu.add(createMenuItem("إعدادات / Settings", null, e -> showSettings()));
        toolsMenu.add(createMenuItem("نسخ احتياطي / Backup", null, e -> createBackup()));
        toolsMenu.add(createMenuItem("استعادة / Restore", null, e -> restoreBackup()));
        
        // Help menu
        JMenu helpMenu = new JMenu("مساعدة / Help");
        helpMenu.add(createMenuItem("دليل المستخدم / User Guide", "F1", e -> showUserGuide()));
        helpMenu.add(createMenuItem("حول / About", null, e -> showAbout()));
        
        menuBar.add(fileMenu);
        menuBar.add(editMenu);
        menuBar.add(viewMenu);
        menuBar.add(toolsMenu);
        menuBar.add(helpMenu);
        
        return menuBar;
    }
    
    /**
     * إنشاء شريط الأدوات
     * Create toolbar
     */
    private JToolBar createToolBar() {
        JToolBar toolBar = new JToolBar();
        toolBar.setFloatable(false);
        
        toolBar.add(createToolBarButton("جديد / New", "📄", e -> newDocument()));
        toolBar.add(createToolBarButton("حفظ / Save", "💾", e -> saveDocument()));
        toolBar.addSeparator();
        toolBar.add(createToolBarButton("تحديث / Refresh", "🔄", e -> refreshCurrentTab()));
        toolBar.add(createToolBarButton("تصدير / Export", "📤", e -> exportData()));
        toolBar.addSeparator();
        toolBar.add(createToolBarButton("إعدادات / Settings", "⚙️", e -> showSettings()));
        
        return toolBar;
    }
    
    /**
     * إنشاء شريط الحالة
     * Create status bar
     */
    private JPanel createStatusBar() {
        JPanel statusBar = new JPanel(new BorderLayout());
        statusBar.setBorder(BorderFactory.createLoweredBevelBorder());
        statusBar.setPreferredSize(new Dimension(0, 25));
        
        JLabel statusLabel = new JLabel("جاهز / Ready");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));
        statusBar.add(statusLabel, BorderLayout.WEST);
        
        JLabel timeLabel = new JLabel();
        timeLabel.setBorder(BorderFactory.createEmptyBorder(2, 5, 2, 5));
        statusBar.add(timeLabel, BorderLayout.EAST);
        
        // Update time every second
        Timer timer = new Timer(1000, e -> {
            timeLabel.setText(java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            ));
        });
        timer.start();
        
        return statusBar;
    }
    
    /**
     * إعداد معالجات الأحداث
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Window closing event
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                exitApplication();
            }
        });
        
        // Tab change event
        tabbedPane.addChangeListener(e -> {
            int selectedIndex = tabbedPane.getSelectedIndex();
            if (selectedIndex >= 0) {
                String tabTitle = tabbedPane.getTitleAt(selectedIndex);
                System.out.println("تم التبديل إلى تبويب: " + tabTitle);
                System.out.println("Switched to tab: " + tabTitle);
            }
        });
    }
    
    // Helper methods for creating UI components
    
    private JMenuItem createMenuItem(String text, String accelerator, ActionListener action) {
        JMenuItem item = new JMenuItem(text);
        if (accelerator != null) {
            item.setAccelerator(KeyStroke.getKeyStroke(accelerator));
        }
        item.addActionListener(action);
        return item;
    }
    
    private JButton createToolBarButton(String tooltip, String icon, ActionListener action) {
        JButton button = new JButton(icon);
        button.setToolTipText(tooltip);
        button.addActionListener(action);
        button.setFocusable(false);
        return button;
    }
    
    private Icon createTabIcon(String emoji) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                g.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 16));
                g.drawString(emoji, x, y + 12);
            }
            
            @Override
            public int getIconWidth() { return 20; }
            
            @Override
            public int getIconHeight() { return 16; }
        };
    }
    
    // Menu action methods
    
    private void newDocument() {
        JOptionPane.showMessageDialog(this, "وظيفة جديد قيد التطوير\nNew function under development");
    }
    
    private void openDocument() {
        JOptionPane.showMessageDialog(this, "وظيفة فتح قيد التطوير\nOpen function under development");
    }
    
    private void saveDocument() {
        JOptionPane.showMessageDialog(this, "وظيفة حفظ قيد التطوير\nSave function under development");
    }
    
    private void exportData() {
        JOptionPane.showMessageDialog(this, "وظيفة تصدير قيد التطوير\nExport function under development");
    }
    
    private void undo() {
        JOptionPane.showMessageDialog(this, "وظيفة تراجع قيد التطوير\nUndo function under development");
    }
    
    private void redo() {
        JOptionPane.showMessageDialog(this, "وظيفة إعادة قيد التطوير\nRedo function under development");
    }
    
    private void copy() {
        JOptionPane.showMessageDialog(this, "وظيفة نسخ قيد التطوير\nCopy function under development");
    }
    
    private void paste() {
        JOptionPane.showMessageDialog(this, "وظيفة لصق قيد التطوير\nPaste function under development");
    }
    
    private void refreshCurrentTab() {
        int selectedIndex = tabbedPane.getSelectedIndex();
        // Refresh the current tab's data
        JOptionPane.showMessageDialog(this, "تم تحديث البيانات\nData refreshed");
    }
    
    private void toggleFullScreen() {
        if (getExtendedState() == JFrame.MAXIMIZED_BOTH) {
            setExtendedState(JFrame.NORMAL);
        } else {
            setExtendedState(JFrame.MAXIMIZED_BOTH);
        }
    }
    
    private void showSettings() {
        JOptionPane.showMessageDialog(this, "نافذة الإعدادات قيد التطوير\nSettings window under development");
    }
    
    private void createBackup() {
        JOptionPane.showMessageDialog(this, "وظيفة النسخ الاحتياطي قيد التطوير\nBackup function under development");
    }
    
    private void restoreBackup() {
        JOptionPane.showMessageDialog(this, "وظيفة الاستعادة قيد التطوير\nRestore function under development");
    }
    
    private void showUserGuide() {
        JOptionPane.showMessageDialog(this, "دليل المستخدم قيد التطوير\nUser guide under development");
    }
    
    private void showAbout() {
        String message = """
            نظام إدارة المبيعات
            Sales Management System
            
            الإصدار / Version: 1.0
            المطور / Developer: Sales Manager Team
            
            نظام شامل لإدارة المبيعات والمخزون
            Comprehensive sales and inventory management system
            """;
        JOptionPane.showMessageDialog(this, message, "حول / About", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void exitApplication() {
        int option = JOptionPane.showConfirmDialog(
            this,
            "هل تريد إغلاق التطبيق؟\nDo you want to exit the application?",
            "تأكيد الخروج / Confirm Exit",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );
        
        if (option == JOptionPane.YES_OPTION) {
            // Close database connection
            DBHelper.closeConnection();
            System.exit(0);
        }
    }
}
