package ui;

import javax.swing.*;
import java.awt.*;

/**
 * صفحة إدارة المنتجات
 * Products management page
 */
public class ProductsPage extends JPanel {
    
    public ProductsPage() {
        initializeComponents();
    }
    
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        JLabel label = new JLabel("صفحة المنتجات قيد التطوير / Products Page Under Development", JLabel.CENTER);
        label.setFont(new Font("Arial", Font.BOLD, 24));
        add(label, BorderLayout.CENTER);
        
        JTextArea infoArea = new JTextArea();
        infoArea.setText("""
            هذه الصفحة ستحتوي على:
            - إدارة المنتجات (إضافة، تعديل، حذف)
            - إدارة المخزون والكميات
            - تحديد أسعار الشراء والبيع
            - تنبيهات المخزون المنخفض
            - تصنيف المنتجات
            
            This page will contain:
            - Product management (add, edit, delete)
            - Inventory and quantity management
            - Purchase and selling price settings
            - Low stock alerts
            - Product categorization
            """);
        infoArea.setEditable(false);
        infoArea.setFont(new Font("Arial", Font.PLAIN, 14));
        infoArea.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        add(infoArea, BorderLayout.SOUTH);
    }
}
