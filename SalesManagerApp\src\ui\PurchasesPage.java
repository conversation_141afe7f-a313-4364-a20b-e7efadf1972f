package ui;

import javax.swing.*;
import java.awt.*;

/**
 * صفحة إدارة المشتريات
 * Purchases management page
 */
public class PurchasesPage extends JPanel {
    
    public PurchasesPage() {
        initializeComponents();
    }
    
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        JLabel label = new JLabel("صفحة المشتريات قيد التطوير / Purchases Page Under Development", JLabel.CENTER);
        label.setFont(new Font("Arial", Font.BOLD, 24));
        add(label, BorderLayout.CENTER);
        
        JTextArea infoArea = new JTextArea();
        infoArea.setText("""
            هذه الصفحة ستحتوي على:
            - إنشاء فواتير الشراء
            - اختيار المورد والمنتجات
            - تحديث أسعار الشراء
            - تحديث المخزون تلقائياً
            - تسجيل المدفوعات للموردين
            - تتبع المشتريات الشهرية
            
            This page will contain:
            - Create purchase invoices
            - Select supplier and products
            - Update purchase prices
            - Automatically update inventory
            - Record payments to suppliers
            - Track monthly purchases
            """);
        infoArea.setEditable(false);
        infoArea.setFont(new Font("Arial", Font.PLAIN, 14));
        infoArea.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        add(infoArea, BorderLayout.SOUTH);
    }
}
