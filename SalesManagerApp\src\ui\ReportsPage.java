package ui;

import javax.swing.*;
import java.awt.*;

/**
 * صفحة التقارير
 * Reports page
 */
public class ReportsPage extends JPanel {
    
    public ReportsPage() {
        initializeComponents();
    }
    
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        JLabel label = new JLabel("صفحة التقارير قيد التطوير / Reports Page Under Development", JLabel.CENTER);
        label.setFont(new Font("Arial", Font.BOLD, 24));
        add(label, BorderLayout.CENTER);
        
        JTextArea infoArea = new JTextArea();
        infoArea.setText("""
            هذه الصفحة ستحتوي على:
            - تقارير المبيعات اليومية والشهرية
            - تقارير الأرباح والخسائر
            - تقارير المخزون
            - تقارير العملاء والديون
            - تقارير الموردين والمستحقات
            - المنتجات الأكثر مبيعاً
            - تصدير التقارير (PDF, Excel)
            
            This page will contain:
            - Daily and monthly sales reports
            - Profit and loss reports
            - Inventory reports
            - Customer and debt reports
            - Supplier and payables reports
            - Best selling products
            - Export reports (PDF, Excel)
            """);
        infoArea.setEditable(false);
        infoArea.setFont(new Font("Arial", Font.PLAIN, 14));
        infoArea.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        add(infoArea, BorderLayout.SOUTH);
    }
}
