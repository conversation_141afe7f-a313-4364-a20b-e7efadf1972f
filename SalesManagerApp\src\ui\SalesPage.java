package ui;

import javax.swing.*;
import java.awt.*;

/**
 * صفحة إدارة المبيعات
 * Sales management page
 */
public class SalesPage extends JPanel {
    
    public SalesPage() {
        initializeComponents();
    }
    
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        JLabel label = new JLabel("صفحة المبيعات قيد التطوير / Sales Page Under Development", JLabel.CENTER);
        label.setFont(new Font("Arial", Font.BOLD, 24));
        add(label, BorderLayout.CENTER);
        
        JTextArea infoArea = new JTextArea();
        infoArea.setText("""
            هذه الصفحة ستحتوي على:
            - إنشاء فواتير البيع
            - اختيار العميل والمنتجات
            - حساب الإجمالي والخصومات
            - تسجيل المدفوعات
            - طباعة الفواتير
            - تتبع المبيعات اليومية
            
            This page will contain:
            - Create sales invoices
            - Select customer and products
            - Calculate totals and discounts
            - Record payments
            - Print invoices
            - Track daily sales
            """);
        infoArea.setEditable(false);
        infoArea.setFont(new Font("Arial", Font.PLAIN, 14));
        infoArea.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        add(infoArea, BorderLayout.SOUTH);
    }
}
