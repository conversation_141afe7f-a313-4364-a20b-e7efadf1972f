package ui;

import javax.swing.*;
import java.awt.*;

/**
 * صفحة إدارة الموردين
 * Suppliers management page
 */
public class SuppliersPage extends JPanel {
    
    public SuppliersPage() {
        initializeComponents();
    }
    
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        JLabel label = new JLabel("صفحة الموردين قيد التطوير / Suppliers Page Under Development", JLabel.CENTER);
        label.setFont(new Font("Arial", Font.BOLD, 24));
        add(label, BorderLayout.CENTER);
        
        JTextArea infoArea = new JTextArea();
        infoArea.setText("""
            هذه الصفحة ستحتوي على:
            - إدارة الموردين (إضافة، تعديل، حذف)
            - تتبع المستحقات للموردين
            - تاريخ التعاملات مع كل مورد
            - تقارير المشتريات حسب المورد
            
            This page will contain:
            - Supplier management (add, edit, delete)
            - Track payables to suppliers
            - Transaction history with each supplier
            - Purchase reports by supplier
            """);
        infoArea.setEditable(false);
        infoArea.setFont(new Font("Arial", Font.PLAIN, 14));
        infoArea.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        add(infoArea, BorderLayout.SOUTH);
    }
}
