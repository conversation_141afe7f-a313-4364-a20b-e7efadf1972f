package utils;

import models.*;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

/**
 * فئة تصدير التقارير
 * Report export utility class
 */
public class Exporter {
    
    /**
     * تصدير قائمة العملاء إلى CSV
     * Export customers list to CSV
     * @param customers List of customers
     * @param filename Output filename
     * @return true if successful, false otherwise
     */
    public static boolean exportCustomersToCSV(List<Customer> customers, String filename) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
            // Write header
            writer.println("ID,Name,Phone,Email,Address,Debt Balance,Created At");
            
            // Write data
            for (Customer customer : customers) {
                writer.printf("%d,\"%s\",\"%s\",\"%s\",\"%s\",%.2f,\"%s\"%n",
                    customer.getId(),
                    escapeCSV(customer.getName()),
                    escapeCSV(customer.getPhone()),
                    escapeCSV(customer.getEmail()),
                    escapeCSV(customer.getAddress()),
                    customer.getDebtBalance(),
                    customer.getCreatedAt()
                );
            }
            
            System.out.println("تم تصدير العملاء بنجاح إلى: " + filename);
            System.out.println("Customers exported successfully to: " + filename);
            return true;
            
        } catch (IOException e) {
            System.err.println("خطأ في تصدير العملاء: " + e.getMessage());
            System.err.println("Error exporting customers: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تصدير قائمة الموردين إلى CSV
     * Export suppliers list to CSV
     * @param suppliers List of suppliers
     * @param filename Output filename
     * @return true if successful, false otherwise
     */
    public static boolean exportSuppliersToCSV(List<Supplier> suppliers, String filename) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
            // Write header
            writer.println("ID,Name,Phone,Email,Address,Payable Balance,Created At");
            
            // Write data
            for (Supplier supplier : suppliers) {
                writer.printf("%d,\"%s\",\"%s\",\"%s\",\"%s\",%.2f,\"%s\"%n",
                    supplier.getId(),
                    escapeCSV(supplier.getName()),
                    escapeCSV(supplier.getPhone()),
                    escapeCSV(supplier.getEmail()),
                    escapeCSV(supplier.getAddress()),
                    supplier.getPayableBalance(),
                    supplier.getCreatedAt()
                );
            }
            
            System.out.println("تم تصدير الموردين بنجاح إلى: " + filename);
            System.out.println("Suppliers exported successfully to: " + filename);
            return true;
            
        } catch (IOException e) {
            System.err.println("خطأ في تصدير الموردين: " + e.getMessage());
            System.err.println("Error exporting suppliers: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تصدير قائمة المنتجات إلى CSV
     * Export products list to CSV
     * @param products List of products
     * @param filename Output filename
     * @return true if successful, false otherwise
     */
    public static boolean exportProductsToCSV(List<Product> products, String filename) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
            // Write header
            writer.println("ID,Name,Description,Category,Purchase Price,Selling Price,Stock Quantity,Min Stock Level,Profit Margin,Created At");
            
            // Write data
            for (Product product : products) {
                writer.printf("%d,\"%s\",\"%s\",\"%s\",%.2f,%.2f,%d,%d,%.2f%%,\"%s\"%n",
                    product.getId(),
                    escapeCSV(product.getName()),
                    escapeCSV(product.getDescription()),
                    escapeCSV(product.getCategory()),
                    product.getPurchasePrice(),
                    product.getSellingPrice(),
                    product.getStockQuantity(),
                    product.getMinStockLevel(),
                    product.getProfitMargin(),
                    product.getCreatedAt()
                );
            }
            
            System.out.println("تم تصدير المنتجات بنجاح إلى: " + filename);
            System.out.println("Products exported successfully to: " + filename);
            return true;
            
        } catch (IOException e) {
            System.err.println("خطأ في تصدير المنتجات: " + e.getMessage());
            System.err.println("Error exporting products: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تصدير قائمة المبيعات إلى CSV
     * Export sales list to CSV
     * @param sales List of sales
     * @param filename Output filename
     * @return true if successful, false otherwise
     */
    public static boolean exportSalesToCSV(List<Sale> sales, String filename) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
            // Write header
            writer.println("ID,Customer ID,Sale Date,Total Amount,Paid Amount,Remaining Amount,Discount,Payment Status,Notes,Created At");
            
            // Write data
            for (Sale sale : sales) {
                writer.printf("%d,%d,\"%s\",%.2f,%.2f,%.2f,%.2f,\"%s\",\"%s\",\"%s\"%n",
                    sale.getId(),
                    sale.getCustomerId(),
                    sale.getSaleDate(),
                    sale.getTotalAmount(),
                    sale.getPaidAmount(),
                    sale.getRemainingAmount(),
                    sale.getDiscount(),
                    Formatter.formatPaymentStatus(sale.getPaidAmount(), sale.getTotalAmount()),
                    escapeCSV(sale.getNotes()),
                    sale.getCreatedAt()
                );
            }
            
            System.out.println("تم تصدير المبيعات بنجاح إلى: " + filename);
            System.out.println("Sales exported successfully to: " + filename);
            return true;
            
        } catch (IOException e) {
            System.err.println("خطأ في تصدير المبيعات: " + e.getMessage());
            System.err.println("Error exporting sales: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تصدير تقرير الأرباح إلى CSV
     * Export profit report to CSV
     * @param reportData Report data map
     * @param filename Output filename
     * @return true if successful, false otherwise
     */
    public static boolean exportProfitReportToCSV(Map<String, Object> reportData, String filename) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
            // Write header
            writer.println("Metric,Value");
            
            // Write summary data
            for (Map.Entry<String, Object> entry : reportData.entrySet()) {
                writer.printf("\"%s\",\"%s\"%n", 
                    escapeCSV(entry.getKey()), 
                    escapeCSV(entry.getValue().toString())
                );
            }
            
            System.out.println("تم تصدير تقرير الأرباح بنجاح إلى: " + filename);
            System.out.println("Profit report exported successfully to: " + filename);
            return true;
            
        } catch (IOException e) {
            System.err.println("خطأ في تصدير تقرير الأرباح: " + e.getMessage());
            System.err.println("Error exporting profit report: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تصدير فاتورة مبيعات إلى HTML
     * Export sales invoice to HTML
     * @param sale Sale object
     * @param customer Customer object
     * @param filename Output filename
     * @return true if successful, false otherwise
     */
    public static boolean exportSalesInvoiceToHTML(Sale sale, Customer customer, String filename) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
            writer.println("<!DOCTYPE html>");
            writer.println("<html dir='rtl' lang='ar'>");
            writer.println("<head>");
            writer.println("<meta charset='UTF-8'>");
            writer.println("<title>فاتورة مبيعات - Sales Invoice</title>");
            writer.println("<style>");
            writer.println("body { font-family: Arial, sans-serif; margin: 20px; }");
            writer.println("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
            writer.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }");
            writer.println("th { background-color: #f2f2f2; }");
            writer.println(".header { text-align: center; margin-bottom: 30px; }");
            writer.println(".total { font-weight: bold; background-color: #f9f9f9; }");
            writer.println("</style>");
            writer.println("</head>");
            writer.println("<body>");
            
            // Header
            writer.println("<div class='header'>");
            writer.println("<h1>فاتورة مبيعات / Sales Invoice</h1>");
            writer.printf("<p>رقم الفاتورة / Invoice #: %d</p>%n", sale.getId());
            writer.printf("<p>التاريخ / Date: %s</p>%n", sale.getSaleDate());
            writer.println("</div>");
            
            // Customer info
            writer.println("<h3>بيانات العميل / Customer Information</h3>");
            writer.println("<table>");
            writer.printf("<tr><td>الاسم / Name:</td><td>%s</td></tr>%n", customer.getName());
            writer.printf("<tr><td>الهاتف / Phone:</td><td>%s</td></tr>%n", customer.getPhone());
            writer.printf("<tr><td>البريد الإلكتروني / Email:</td><td>%s</td></tr>%n", customer.getEmail());
            writer.printf("<tr><td>العنوان / Address:</td><td>%s</td></tr>%n", customer.getAddress());
            writer.println("</table>");
            
            // Sale items (if loaded)
            if (sale.getSaleItems() != null && !sale.getSaleItems().isEmpty()) {
                writer.println("<h3>تفاصيل الفاتورة / Invoice Details</h3>");
                writer.println("<table>");
                writer.println("<tr><th>المنتج / Product</th><th>الكمية / Quantity</th><th>السعر / Unit Price</th><th>الإجمالي / Total</th></tr>");
                
                for (SaleItem item : sale.getSaleItems()) {
                    writer.printf("<tr><td>Product ID: %d</td><td>%d</td><td>%s</td><td>%s</td></tr>%n",
                        item.getProductId(),
                        item.getQuantity(),
                        Formatter.formatCurrency(item.getUnitPrice()),
                        Formatter.formatCurrency(item.getTotalPrice())
                    );
                }
                writer.println("</table>");
            }
            
            // Totals
            writer.println("<table>");
            writer.printf("<tr class='total'><td>الخصم / Discount:</td><td>%s</td></tr>%n", 
                Formatter.formatCurrency(sale.getDiscount()));
            writer.printf("<tr class='total'><td>الإجمالي / Total:</td><td>%s</td></tr>%n", 
                Formatter.formatCurrency(sale.getTotalAmount()));
            writer.printf("<tr class='total'><td>المدفوع / Paid:</td><td>%s</td></tr>%n", 
                Formatter.formatCurrency(sale.getPaidAmount()));
            writer.printf("<tr class='total'><td>المتبقي / Remaining:</td><td>%s</td></tr>%n", 
                Formatter.formatCurrency(sale.getRemainingAmount()));
            writer.println("</table>");
            
            if (sale.getNotes() != null && !sale.getNotes().trim().isEmpty()) {
                writer.printf("<p><strong>ملاحظات / Notes:</strong> %s</p>%n", sale.getNotes());
            }
            
            writer.println("</body>");
            writer.println("</html>");
            
            System.out.println("تم تصدير الفاتورة بنجاح إلى: " + filename);
            System.out.println("Invoice exported successfully to: " + filename);
            return true;
            
        } catch (IOException e) {
            System.err.println("خطأ في تصدير الفاتورة: " + e.getMessage());
            System.err.println("Error exporting invoice: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * تنظيف النص للاستخدام في CSV
     * Escape text for CSV usage
     * @param text Text to escape
     * @return Escaped text
     */
    private static String escapeCSV(String text) {
        if (text == null) return "";
        // Replace quotes with double quotes and wrap in quotes if contains comma or quote
        if (text.contains("\"") || text.contains(",") || text.contains("\n")) {
            return text.replace("\"", "\"\"");
        }
        return text;
    }
    
    /**
     * إنشاء اسم ملف فريد بالتاريخ والوقت
     * Generate unique filename with timestamp
     * @param prefix File prefix
     * @param extension File extension
     * @return Unique filename
     */
    public static String generateUniqueFilename(String prefix, String extension) {
        String timestamp = Formatter.getCurrentDateTime().replace(":", "-").replace(" ", "_");
        return prefix + "_" + timestamp + "." + extension;
    }
}
