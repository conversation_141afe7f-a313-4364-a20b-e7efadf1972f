package utils;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * فئة تنسيق البيانات
 * Data formatting utility class
 */
public class Formatter {
    
    // Date formatters
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat DISPLAY_DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy");
    private static final SimpleDateFormat DISPLAY_DATETIME_FORMAT = new SimpleDateFormat("dd/MM/yyyy HH:mm");
    
    // Number formatters
    private static final DecimalFormat CURRENCY_FORMAT = new DecimalFormat("#,##0.00");
    private static final DecimalFormat PERCENTAGE_FORMAT = new DecimalFormat("#0.00%");
    private static final DecimalFormat INTEGER_FORMAT = new DecimalFormat("#,##0");
    private static final NumberFormat CURRENCY_LOCALE_FORMAT = NumberFormat.getCurrencyInstance(new Locale("ar", "SA"));
    
    /**
     * تنسيق العملة
     * Format currency amount
     * @param amount Amount to format
     * @return Formatted currency string
     */
    public static String formatCurrency(double amount) {
        return CURRENCY_FORMAT.format(amount) + " ريال";
    }
    
    /**
     * تنسيق العملة باستخدام إعدادات المنطقة
     * Format currency using locale settings
     * @param amount Amount to format
     * @return Formatted currency string
     */
    public static String formatCurrencyLocale(double amount) {
        return CURRENCY_LOCALE_FORMAT.format(amount);
    }
    
    /**
     * تنسيق النسبة المئوية
     * Format percentage
     * @param value Value to format (0.25 = 25%)
     * @return Formatted percentage string
     */
    public static String formatPercentage(double value) {
        return PERCENTAGE_FORMAT.format(value / 100);
    }
    
    /**
     * تنسيق الأرقام الصحيحة
     * Format integer numbers
     * @param number Number to format
     * @return Formatted number string
     */
    public static String formatInteger(int number) {
        return INTEGER_FORMAT.format(number);
    }
    
    /**
     * تنسيق الأرقام العشرية
     * Format decimal numbers
     * @param number Number to format
     * @param decimalPlaces Number of decimal places
     * @return Formatted number string
     */
    public static String formatDecimal(double number, int decimalPlaces) {
        String pattern = "#,##0.";
        for (int i = 0; i < decimalPlaces; i++) {
            pattern += "0";
        }
        DecimalFormat formatter = new DecimalFormat(pattern);
        return formatter.format(number);
    }
    
    /**
     * تنسيق التاريخ للعرض
     * Format date for display (dd/MM/yyyy)
     * @param date Date to format
     * @return Formatted date string
     */
    public static String formatDisplayDate(Date date) {
        if (date == null) return "";
        return DISPLAY_DATE_FORMAT.format(date);
    }
    
    /**
     * تنسيق التاريخ والوقت للعرض
     * Format datetime for display (dd/MM/yyyy HH:mm)
     * @param date Date to format
     * @return Formatted datetime string
     */
    public static String formatDisplayDateTime(Date date) {
        if (date == null) return "";
        return DISPLAY_DATETIME_FORMAT.format(date);
    }
    
    /**
     * تنسيق التاريخ لقاعدة البيانات
     * Format date for database (yyyy-MM-dd)
     * @param date Date to format
     * @return Formatted date string
     */
    public static String formatDatabaseDate(Date date) {
        if (date == null) return null;
        return DATE_FORMAT.format(date);
    }
    
    /**
     * تنسيق التاريخ والوقت لقاعدة البيانات
     * Format datetime for database (yyyy-MM-dd HH:mm:ss)
     * @param date Date to format
     * @return Formatted datetime string
     */
    public static String formatDatabaseDateTime(Date date) {
        if (date == null) return null;
        return DATETIME_FORMAT.format(date);
    }
    
    /**
     * الحصول على التاريخ الحالي بتنسيق قاعدة البيانات
     * Get current date in database format
     * @return Current date string
     */
    public static String getCurrentDate() {
        return formatDatabaseDate(new Date());
    }
    
    /**
     * الحصول على التاريخ والوقت الحالي بتنسيق قاعدة البيانات
     * Get current datetime in database format
     * @return Current datetime string
     */
    public static String getCurrentDateTime() {
        return formatDatabaseDateTime(new Date());
    }
    
    /**
     * تنسيق رقم الهاتف
     * Format phone number
     * @param phone Phone number to format
     * @return Formatted phone number
     */
    public static String formatPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return "";
        }
        
        // Remove all non-digit characters except +
        String cleaned = phone.replaceAll("[^+\\d]", "");
        
        // Format Saudi phone numbers
        if (cleaned.startsWith("+966")) {
            return "+966 " + cleaned.substring(4, 7) + " " + cleaned.substring(7);
        } else if (cleaned.startsWith("966")) {
            return "+966 " + cleaned.substring(3, 6) + " " + cleaned.substring(6);
        } else if (cleaned.startsWith("05")) {
            return "05" + cleaned.substring(2, 4) + " " + cleaned.substring(4);
        }
        
        return phone; // Return original if no pattern matches
    }
    
    /**
     * تنسيق النص للعرض (تحديد الطول الأقصى)
     * Format text for display (limit length)
     * @param text Text to format
     * @param maxLength Maximum length
     * @return Formatted text
     */
    public static String formatTextForDisplay(String text, int maxLength) {
        if (text == null) return "";
        if (text.length() <= maxLength) return text;
        return text.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * تنسيق حالة الدفع
     * Format payment status
     * @param paidAmount Paid amount
     * @param totalAmount Total amount
     * @return Payment status string
     */
    public static String formatPaymentStatus(double paidAmount, double totalAmount) {
        if (paidAmount >= totalAmount) {
            return "مدفوع بالكامل / Fully Paid";
        } else if (paidAmount > 0) {
            return "مدفوع جزئياً / Partially Paid";
        } else {
            return "غير مدفوع / Unpaid";
        }
    }
    
    /**
     * تنسيق حالة المخزون
     * Format stock status
     * @param currentStock Current stock quantity
     * @param minStock Minimum stock level
     * @return Stock status string
     */
    public static String formatStockStatus(int currentStock, int minStock) {
        if (currentStock <= 0) {
            return "نفد المخزون / Out of Stock";
        } else if (currentStock <= minStock) {
            return "مخزون منخفض / Low Stock";
        } else {
            return "متوفر / In Stock";
        }
    }
    
    /**
     * تنسيق هامش الربح
     * Format profit margin
     * @param purchasePrice Purchase price
     * @param sellingPrice Selling price
     * @return Profit margin string
     */
    public static String formatProfitMargin(double purchasePrice, double sellingPrice) {
        if (purchasePrice == 0) {
            return "غير محدد / N/A";
        }
        double margin = ((sellingPrice - purchasePrice) / purchasePrice) * 100;
        return formatDecimal(margin, 2) + "%";
    }
    
    /**
     * تنسيق الحجم بالبايت
     * Format file size in bytes
     * @param bytes Size in bytes
     * @return Formatted size string
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return formatDecimal(bytes / 1024.0, 1) + " KB";
        if (bytes < 1024 * 1024 * 1024) return formatDecimal(bytes / (1024.0 * 1024), 1) + " MB";
        return formatDecimal(bytes / (1024.0 * 1024 * 1024), 1) + " GB";
    }
    
    /**
     * إزالة تنسيق العملة والحصول على الرقم
     * Remove currency formatting and get number
     * @param formattedCurrency Formatted currency string
     * @return Numeric value
     */
    public static double parseCurrency(String formattedCurrency) {
        if (formattedCurrency == null || formattedCurrency.trim().isEmpty()) {
            return 0.0;
        }
        
        // Remove currency symbols and formatting
        String cleaned = formattedCurrency.replaceAll("[^\\d.-]", "");
        try {
            return Double.parseDouble(cleaned);
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }
}
