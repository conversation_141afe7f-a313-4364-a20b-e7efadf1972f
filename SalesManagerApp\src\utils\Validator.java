package utils;

import java.util.regex.Pattern;

/**
 * فئة التحقق من صحة البيانات
 * Data validation utility class
 */
public class Validator {
    
    // Regular expressions for validation
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^[+]?[0-9\\s\\-\\(\\)]{7,15}$"
    );
    
    private static final Pattern NAME_PATTERN = Pattern.compile(
        "^[a-zA-Z\\u0600-\\u06FF\\s]{2,50}$"
    );
    
    /**
     * التحقق من صحة البريد الإلكتروني
     * Validate email address
     * @param email Email to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email.trim()).matches();
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     * Validate phone number
     * @param phone Phone number to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        return PHONE_PATTERN.matcher(phone.trim()).matches();
    }
    
    /**
     * التحقق من صحة الاسم
     * Validate name (supports Arabic and English)
     * @param name Name to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return NAME_PATTERN.matcher(name.trim()).matches();
    }
    
    /**
     * التحقق من أن النص غير فارغ
     * Check if text is not empty
     * @param text Text to check
     * @return true if not empty, false otherwise
     */
    public static boolean isNotEmpty(String text) {
        return text != null && !text.trim().isEmpty();
    }
    
    /**
     * التحقق من أن النص ضمن طول معين
     * Check if text length is within limits
     * @param text Text to check
     * @param minLength Minimum length
     * @param maxLength Maximum length
     * @return true if within limits, false otherwise
     */
    public static boolean isValidLength(String text, int minLength, int maxLength) {
        if (text == null) {
            return false;
        }
        int length = text.trim().length();
        return length >= minLength && length <= maxLength;
    }
    
    /**
     * التحقق من صحة السعر
     * Validate price (must be positive)
     * @param price Price to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidPrice(double price) {
        return price >= 0;
    }
    
    /**
     * التحقق من صحة الكمية
     * Validate quantity (must be positive integer)
     * @param quantity Quantity to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidQuantity(int quantity) {
        return quantity >= 0;
    }
    
    /**
     * التحقق من صحة المعرف
     * Validate ID (must be positive)
     * @param id ID to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidId(int id) {
        return id > 0;
    }
    
    /**
     * التحقق من صحة النسبة المئوية
     * Validate percentage (0-100)
     * @param percentage Percentage to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidPercentage(double percentage) {
        return percentage >= 0 && percentage <= 100;
    }
    
    /**
     * التحقق من صحة الخصم
     * Validate discount amount
     * @param discount Discount amount
     * @param totalAmount Total amount
     * @return true if valid, false otherwise
     */
    public static boolean isValidDiscount(double discount, double totalAmount) {
        return discount >= 0 && discount <= totalAmount;
    }
    
    /**
     * التحقق من صحة المبلغ المدفوع
     * Validate paid amount
     * @param paidAmount Paid amount
     * @param totalAmount Total amount
     * @return true if valid, false otherwise
     */
    public static boolean isValidPaidAmount(double paidAmount, double totalAmount) {
        return paidAmount >= 0 && paidAmount <= totalAmount;
    }
    
    /**
     * تنظيف النص من المسافات الزائدة
     * Clean text by trimming whitespace
     * @param text Text to clean
     * @return Cleaned text or null if input was null
     */
    public static String cleanText(String text) {
        return text != null ? text.trim() : null;
    }
    
    /**
     * التحقق من صحة تاريخ بصيغة YYYY-MM-DD
     * Validate date in YYYY-MM-DD format
     * @param date Date string to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidDate(String date) {
        if (date == null || date.trim().isEmpty()) {
            return false;
        }
        
        Pattern datePattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
        if (!datePattern.matcher(date).matches()) {
            return false;
        }
        
        try {
            String[] parts = date.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            int day = Integer.parseInt(parts[2]);
            
            // Basic date validation
            if (year < 1900 || year > 2100) return false;
            if (month < 1 || month > 12) return false;
            if (day < 1 || day > 31) return false;
            
            // More specific validation for months with fewer days
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
                return false;
            }
            if (month == 2) {
                boolean isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
                if (day > (isLeapYear ? 29 : 28)) {
                    return false;
                }
            }
            
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * الحصول على رسالة خطأ للبريد الإلكتروني
     * Get error message for email validation
     * @return Error message
     */
    public static String getEmailErrorMessage() {
        return "البريد الإلكتروني غير صحيح / Invalid email format";
    }
    
    /**
     * الحصول على رسالة خطأ لرقم الهاتف
     * Get error message for phone validation
     * @return Error message
     */
    public static String getPhoneErrorMessage() {
        return "رقم الهاتف غير صحيح / Invalid phone number format";
    }
    
    /**
     * الحصول على رسالة خطأ للاسم
     * Get error message for name validation
     * @return Error message
     */
    public static String getNameErrorMessage() {
        return "الاسم يجب أن يكون بين 2-50 حرف / Name must be 2-50 characters";
    }
    
    /**
     * الحصول على رسالة خطأ للحقل المطلوب
     * Get error message for required field
     * @param fieldName Field name
     * @return Error message
     */
    public static String getRequiredFieldMessage(String fieldName) {
        return fieldName + " مطلوب / " + fieldName + " is required";
    }
    
    /**
     * الحصول على رسالة خطأ للسعر
     * Get error message for price validation
     * @return Error message
     */
    public static String getPriceErrorMessage() {
        return "السعر يجب أن يكون رقم موجب / Price must be a positive number";
    }
    
    /**
     * الحصول على رسالة خطأ للكمية
     * Get error message for quantity validation
     * @return Error message
     */
    public static String getQuantityErrorMessage() {
        return "الكمية يجب أن تكون رقم موجب / Quantity must be a positive number";
    }
}
