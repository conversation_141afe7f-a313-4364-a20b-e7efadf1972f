@echo off
echo Running Sales Manager Application Tests...
echo تشغيل اختبارات تطبيق إدارة المبيعات...

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo خطأ: جافا غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

REM Create output directory
if not exist "build" mkdir build

REM Compile test application
echo Compiling test application...
echo تجميع تطبيق الاختبار...
javac -cp "lib/sqlite-jdbc.jar" -d build TestApp.java src/**/*.java

if %errorlevel% neq 0 (
    echo Compilation failed!
    echo فشل التجميع!
    pause
    exit /b 1
)

REM Run tests
echo Running tests...
echo تشغيل الاختبارات...
java -cp "build;lib/sqlite-jdbc.jar" TestApp

echo.
echo Tests completed. Check output above for results.
echo اكتملت الاختبارات. تحقق من النتائج أعلاه.
pause
