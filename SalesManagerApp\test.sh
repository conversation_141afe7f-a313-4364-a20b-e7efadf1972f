#!/bin/bash

echo "Running Sales Manager Application Tests..."
echo "تشغيل اختبارات تطبيق إدارة المبيعات..."

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "خطأ: جافا غير مثبت أو غير موجود في PATH"
    exit 1
fi

# Create output directory
mkdir -p build

# Compile test application
echo "Compiling test application..."
echo "تجميع تطبيق الاختبار..."
javac -cp "lib/sqlite-jdbc.jar" -d build TestApp.java src/**/*.java

if [ $? -ne 0 ]; then
    echo "Compilation failed!"
    echo "فشل التجميع!"
    exit 1
fi

# Run tests
echo "Running tests..."
echo "تشغيل الاختبارات..."
java -cp "build:lib/sqlite-jdbc.jar" TestApp

echo ""
echo "Tests completed. Check output above for results."
echo "اكتملت الاختبارات. تحقق من النتائج أعلاه."
