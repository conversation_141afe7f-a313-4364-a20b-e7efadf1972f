<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المبيعات - Sales Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin-bottom: 0.5rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 1rem;
            background: #e9ecef;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            border-left: 1px solid #ddd;
        }
        
        .tab:first-child {
            border-left: none;
        }
        
        .tab.active {
            background: white;
            color: #667eea;
            font-weight: bold;
        }
        
        .tab:hover {
            background: #f8f9fa;
        }
        
        .content {
            background: white;
            padding: 2rem;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin-left: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 2rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            display: none;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .coming-soon {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .coming-soon h2 {
            margin-bottom: 1rem;
            color: #495057;
        }
        
        .feature-list {
            text-align: right;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>نظام إدارة المبيعات</h1>
        <p>Sales Management System - Demo Version</p>
    </div>
    
    <div class="container">
        <div class="tabs">
            <button class="tab active" onclick="showTab('customers')">👥 العملاء / Customers</button>
            <button class="tab" onclick="showTab('suppliers')">🏢 الموردين / Suppliers</button>
            <button class="tab" onclick="showTab('products')">📦 المنتجات / Products</button>
            <button class="tab" onclick="showTab('sales')">💰 المبيعات / Sales</button>
            <button class="tab" onclick="showTab('reports')">📊 التقارير / Reports</button>
        </div>
        
        <div class="content">
            <!-- Customers Tab -->
            <div id="customers" class="tab-content active">
                <h2>إدارة العملاء / Customer Management</h2>
                
                <div class="alert alert-success" id="success-alert"></div>
                <div class="alert alert-error" id="error-alert"></div>
                
                <form id="customer-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>الاسم / Name *</label>
                            <input type="text" id="customer-name" required>
                        </div>
                        <div class="form-group">
                            <label>الهاتف / Phone</label>
                            <input type="tel" id="customer-phone">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>البريد الإلكتروني / Email</label>
                            <input type="email" id="customer-email">
                        </div>
                        <div class="form-group">
                            <label>العنوان / Address</label>
                            <input type="text" id="customer-address">
                        </div>
                    </div>
                    <div style="margin-top: 1rem;">
                        <button type="submit" class="btn btn-success">إضافة / Add</button>
                        <button type="button" class="btn btn-primary" onclick="updateCustomer()" style="display:none;" id="update-btn">تحديث / Update</button>
                        <button type="button" class="btn btn-secondary" onclick="clearForm()">مسح / Clear</button>
                    </div>
                </form>
                
                <table class="table">
                    <thead>
                        <tr>
                            <th>الاسم / Name</th>
                            <th>الهاتف / Phone</th>
                            <th>البريد / Email</th>
                            <th>العنوان / Address</th>
                            <th>الإجراءات / Actions</th>
                        </tr>
                    </thead>
                    <tbody id="customers-table">
                        <!-- Customer data will be inserted here -->
                    </tbody>
                </table>
            </div>
            
            <!-- Other tabs -->
            <div id="suppliers" class="tab-content">
                <div class="coming-soon">
                    <h2>صفحة الموردين قيد التطوير</h2>
                    <h3>Suppliers Page Under Development</h3>
                    <ul class="feature-list">
                        <li>إدارة الموردين (إضافة، تعديل، حذف)</li>
                        <li>تتبع المستحقات للموردين</li>
                        <li>تاريخ التعاملات مع كل مورد</li>
                        <li>تقارير المشتريات حسب المورد</li>
                    </ul>
                </div>
            </div>
            
            <div id="products" class="tab-content">
                <div class="coming-soon">
                    <h2>صفحة المنتجات قيد التطوير</h2>
                    <h3>Products Page Under Development</h3>
                    <ul class="feature-list">
                        <li>إدارة المنتجات (إضافة، تعديل، حذف)</li>
                        <li>إدارة المخزون والكميات</li>
                        <li>تحديد أسعار الشراء والبيع</li>
                        <li>تنبيهات المخزون المنخفض</li>
                        <li>تصنيف المنتجات</li>
                    </ul>
                </div>
            </div>
            
            <div id="sales" class="tab-content">
                <div class="coming-soon">
                    <h2>صفحة المبيعات قيد التطوير</h2>
                    <h3>Sales Page Under Development</h3>
                    <ul class="feature-list">
                        <li>إنشاء فواتير البيع</li>
                        <li>اختيار العميل والمنتجات</li>
                        <li>حساب الإجمالي والخصومات</li>
                        <li>تسجيل المدفوعات</li>
                        <li>طباعة الفواتير</li>
                    </ul>
                </div>
            </div>
            
            <div id="reports" class="tab-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>إجمالي العملاء</h3>
                        <div class="number" id="total-customers">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>إجمالي المبيعات</h3>
                        <div class="number">0 ريال</div>
                    </div>
                    <div class="stat-card">
                        <h3>المنتجات</h3>
                        <div class="number">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>الموردين</h3>
                        <div class="number">0</div>
                    </div>
                </div>
                
                <div class="coming-soon">
                    <h2>التقارير المتقدمة قيد التطوير</h2>
                    <h3>Advanced Reports Under Development</h3>
                    <ul class="feature-list">
                        <li>تقارير المبيعات اليومية والشهرية</li>
                        <li>تقارير الأرباح والخسائر</li>
                        <li>تقارير المخزون</li>
                        <li>تصدير التقارير (PDF, Excel)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample data storage
        let customers = [];
        let editingCustomerId = null;

        // Tab switching
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Customer management
        document.getElementById('customer-form').addEventListener('submit', function(e) {
            e.preventDefault();
            addCustomer();
        });

        function addCustomer() {
            const name = document.getElementById('customer-name').value;
            const phone = document.getElementById('customer-phone').value;
            const email = document.getElementById('customer-email').value;
            const address = document.getElementById('customer-address').value;

            if (!name.trim()) {
                showAlert('يرجى إدخال اسم العميل / Please enter customer name', 'error');
                return;
            }

            const customer = {
                id: Date.now(),
                name: name.trim(),
                phone: phone.trim(),
                email: email.trim(),
                address: address.trim(),
                createdAt: new Date().toLocaleDateString('ar-SA')
            };

            customers.push(customer);
            renderCustomers();
            clearForm();
            showAlert('تم إضافة العميل بنجاح / Customer added successfully', 'success');
            updateStats();
        }

        function renderCustomers() {
            const tbody = document.getElementById('customers-table');
            tbody.innerHTML = '';

            customers.forEach(customer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.name}</td>
                    <td>${customer.phone}</td>
                    <td>${customer.email}</td>
                    <td>${customer.address}</td>
                    <td>
                        <button class="btn btn-primary" onclick="editCustomer(${customer.id})">تعديل</button>
                        <button class="btn btn-danger" onclick="deleteCustomer(${customer.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function editCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (customer) {
                document.getElementById('customer-name').value = customer.name;
                document.getElementById('customer-phone').value = customer.phone;
                document.getElementById('customer-email').value = customer.email;
                document.getElementById('customer-address').value = customer.address;
                
                editingCustomerId = id;
                document.querySelector('button[type="submit"]').style.display = 'none';
                document.getElementById('update-btn').style.display = 'inline-block';
            }
        }

        function updateCustomer() {
            const customer = customers.find(c => c.id === editingCustomerId);
            if (customer) {
                customer.name = document.getElementById('customer-name').value.trim();
                customer.phone = document.getElementById('customer-phone').value.trim();
                customer.email = document.getElementById('customer-email').value.trim();
                customer.address = document.getElementById('customer-address').value.trim();
                
                renderCustomers();
                clearForm();
                showAlert('تم تحديث العميل بنجاح / Customer updated successfully', 'success');
            }
        }

        function deleteCustomer(id) {
            if (confirm('هل تريد حذف هذا العميل؟ / Do you want to delete this customer?')) {
                customers = customers.filter(c => c.id !== id);
                renderCustomers();
                showAlert('تم حذف العميل بنجاح / Customer deleted successfully', 'success');
                updateStats();
            }
        }

        function clearForm() {
            document.getElementById('customer-form').reset();
            editingCustomerId = null;
            document.querySelector('button[type="submit"]').style.display = 'inline-block';
            document.getElementById('update-btn').style.display = 'none';
        }

        function showAlert(message, type) {
            const alertId = type === 'success' ? 'success-alert' : 'error-alert';
            const alert = document.getElementById(alertId);
            alert.textContent = message;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }

        function updateStats() {
            document.getElementById('total-customers').textContent = customers.length;
        }

        // Initialize with sample data
        function initSampleData() {
            customers = [
                {
                    id: 1,
                    name: 'أحمد محمد',
                    phone: '0501234567',
                    email: '<EMAIL>',
                    address: 'الرياض',
                    createdAt: new Date().toLocaleDateString('ar-SA')
                },
                {
                    id: 2,
                    name: 'فاطمة علي',
                    phone: '0509876543',
                    email: '<EMAIL>',
                    address: 'جدة',
                    createdAt: new Date().toLocaleDateString('ar-SA')
                }
            ];
            renderCustomers();
            updateStats();
        }

        // Initialize the app
        initSampleData();
    </script>
</body>
</html>
